package com.wifochina.modules.manage.cloudlargescreen.service.impl;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.EmsFieldEnum;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.constants.TimePointEnum;
import com.wifochina.common.enums.MemoryCacheEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EmsStatusEnum;
import com.wifochina.common.util.ParseCapacity;
import com.wifochina.common.util.StringUtil;
import com.wifochina.modules.carbon.VO.CarbonTotal;
import com.wifochina.modules.carbon.timer.TaskCarbonTimer;
import com.wifochina.modules.client.InfluxClient;
import com.wifochina.modules.collect.CacheElectricCollectVo;
import com.wifochina.modules.collect.ElectricCollectVo;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.diagram.VO.ValueVO;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.diagram.request.RequestWithDeviceId;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.income.AbstractProfitService;
import com.wifochina.modules.income.IIncomeMemoryCacheServiceKt;
import com.wifochina.modules.income.NewOperationProfitService;
import com.wifochina.modules.income.timer.TaskOperationTimer;
import com.wifochina.modules.income.vo.OperationProfitVo;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenProjectSearch;
import com.wifochina.modules.manage.cloudlargescreen.request.CloudLargeScreenSearch;
import com.wifochina.modules.manage.cloudlargescreen.service.CloudLargeScreenService;
import com.wifochina.modules.manage.cloudlargescreen.vo.*;
import com.wifochina.modules.oauth.AuthUser;
import com.wifochina.modules.oauth.util.SecurityUtil;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.VO.ProfitVO;
import com.wifochina.modules.operation.VO.TotalProfitVO;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.project.service.ProjectServiceKt;
import com.wifochina.modules.project.vo.ProjectManageVo;
import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.service.DayReportCacheService;
import com.wifochina.modules.report.service.DayReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created on 2023/10/12 16:18. 云端大屏接口的ServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CloudLargeScreenServiceImpl implements CloudLargeScreenService {

    private final AbstractProfitService abstractProfitService;

    private final ProjectExtService projectExtService;

    private final ProjectService projectService;

    private final InfluxClient influxClient;
    private final DeviceService deviceService;
    private final ProjectServiceKt projectServiceKt;
    private final NewOperationProfitService newOperationProfitService;
    private final DayReportService dayReportService;
    private final DayReportCacheService dayReportCacheService;
    private final IIncomeMemoryCacheServiceKt incomeMemoryCacheServiceKt;

    /**
     * @param timePointEnum : 时间点
     * @param projectEntity : 项目
     * @return : ProjectProfitCarbonOverviewVo : 项目收益和碳足迹概览
     * @see TaskOperationTimer#cacheTodayProfit()
     * @see AbstractProfitService#totalProfit(ElectricProfitVO, String)
     */
    @Override
    public ProjectProfitCarbonOverviewVo projectProfitAndCarbon(
            TimePointEnum timePointEnum, ProjectEntity projectEntity) {
        ProjectProfitCarbonOverviewVo result = new ProjectProfitCarbonOverviewVo();
        result.setProjectId(projectEntity.getId()).setProjectName(projectEntity.getProjectName());
        // 目前只提供 查询Total的 还未提供其他时间点的查询
        if (EmsConstants.TIME_POINT_ENUM_MAP.get(TimePointEnum.TOTAL.getValue()) != null) {
            // 获取ElectricProfitVO 由于这里已经缓存了今天的相关数据所以很快
            ElectricProfitVO todayElectricProfitVo =
                    TaskOperationTimer.todayElectricMap.get(projectEntity.getId());
            // 复用 totalProfit 方法 内部会从totalElectricMap获取到项目初始到昨天的数据 + todayElectricProfitVo
            newOperationProfitService.getTotalOperationProfit(
                    true, projectEntity.getId(), TimePointEnum.TOTAL);
            TotalProfitVO totalProfitVO =
                    abstractProfitService.totalProfit(todayElectricProfitVo, projectEntity.getId());
            result.setTotalProfitVO(totalProfitVO);
            // 获取CarbonTotal 从缓存中获取
            CarbonTotal carbonTotal = TaskCarbonTimer.carbonTotalMap.get(projectEntity.getId());
            // 兼容一下 好像前端 这个是null 就直接列表不展示了
            if (carbonTotal == null) {
                carbonTotal = new CarbonTotal();
            }
            result.setCarbonTotal(carbonTotal);
        }
        return result;
    }

    /**
     * 查询所有项目的容量(装机,功率,电站数等..)
     *
     * @return : ProjectCapacityOverviewVo
     */
    @Override
    public ProjectCapacityOverviewVo projectAllCapacity(CloudLargeScreenSearch search) {
        List<ProjectEntity> projectEntities = getUserCloudLargeScreenProjects(search);
        // 可看到的projectIds
        List<String> projectIds =
                projectEntities.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        // 查询 可看到的project 项目 未删除的 并且带 项目规模size的
        List<ProjectManageVo> projectManageVos =
                projectExtService.queryAllNotDeleteProjectSize(projectIds);
        // 获取当前年份的 项目
        List<ProjectManageVo> currentYearProjectList =
                projectManageVos.stream()
                        .filter(
                                projectManageVo ->
                                        MyTimeUtil.isInCurrentYear(
                                                projectManageVo.getCreateTime(),
                                                projectManageVo.getTimezone(),
                                                TimeUnit.SECONDS))
                        .collect(Collectors.toList());
        // 计算 容量和功率
        Pair<Long, Long> allPair = calculateCapacityAndPower(projectManageVos);
        Long sumCapacity = allPair.getFirst();
        Long sumPower = allPair.getSecond();
        // 计算 今年新增的 容量和功率
        Pair<Long, Long> currentYearPair = calculateCapacityAndPower(currentYearProjectList);
        Long currentYearCapacity = currentYearPair.getFirst();
        Long currentYearPower = currentYearPair.getSecond();

        // 计算 充放电量
        Pair<Double, Double> allQuantityPair = calculateQuantity(projectManageVos);
        Double sumChargeQuantity = allQuantityPair.getFirst();
        Double sumDischargeQuantity = allQuantityPair.getSecond();
        // 计算 今年新增的 充放电量
        Pair<Double, Double> currentYearQuantityPair = calculateQuantity(currentYearProjectList);
        Double currentYearChargeQuantity = currentYearQuantityPair.getFirst();
        Double currentYearDischargeQuantity = currentYearQuantityPair.getSecond();

        // 构建返回结果
        return new ProjectCapacityOverviewVo()
                .setSumEmsCapacity(sumCapacity)
                .setSumEmsDesignPower(sumPower)
                .setCurrentYearEmsCapacity(currentYearCapacity)
                .setCurrentYearEmsDesignPower(currentYearPower)
                .setSumChargeQuantity(sumChargeQuantity)
                .setSumDischargeQuantity(sumDischargeQuantity)
                .setCurrentYearChargeQuantity(currentYearChargeQuantity)
                .setCurrentYearDischargeQuantity(currentYearDischargeQuantity)
                .setPowerStationNum(projectManageVos.size())
                .setCurrentYearPowerStationNum(currentYearProjectList.size());
    }

    /**
     * 查询所有项目的日收益,月收益和总计收益 + 总计碳减排
     *
     * @return : ProjectAllProfitCarbonOverviewVo
     * @see CloudLargeScreenService#projectProfitAndCarbon(TimePointEnum, ProjectEntity)
     */
    @Override
    public ProjectAllProfitCarbonOverviewVo projectAllProfitCarbon(CloudLargeScreenSearch search) {
        List<ProjectEntity> list = getUserCloudLargeScreenProjects(search);
        ProjectAllProfitCarbonOverviewVo result = new ProjectAllProfitCarbonOverviewVo();
        for (ProjectEntity projectEntity : list) {
            try {
                // 获取到的是 总收益和总碳减排 这个调用了另外一个接口的使用的方法
                ProjectProfitCarbonOverviewVo projectProfitCarbonOverviewVo =
                        this.projectProfitAndCarbon(TimePointEnum.TOTAL, projectEntity);
                // 1.获取总的收益
                // 总收益 , 直接使用total_benefit
                TotalProfitVO totalProfitVO = projectProfitCarbonOverviewVo.getTotalProfitVO();
                // 总碳减排
                CarbonTotal carbonTotal = projectProfitCarbonOverviewVo.getCarbonTotal();
                // 2.获取今日收益
                //                TotalProfitVO todayProfitVo =
                //
                // TaskOperationTimer.todayProfitMap.get(projectEntity.getId());
                OperationProfitVo todayProfitVo =
                        TaskOperationTimer.newTodayProfitMap.get(projectEntity.getId());
                // 1.4.2 优化的 下面的使用地方先不用了 换成了getTotalOperationProfit方法所以暂时注释了这个
                //                ElectricProfitVO todayElectricProfitVo =
                //
                // TaskOperationTimer.todayElectricMap.get(projectEntity.getId());
                // 3.获取月度收益
                Map<String, Object> totalOperationProfit =
                        newOperationProfitService.getTotalOperationProfit(
                                true, projectEntity.getId(), TimePointEnum.MONTH);
                Object monthProfit = totalOperationProfit.get(TimePointEnum.MONTH.getValue());
                // OperationProfitVo
                //                TotalProfitVO monthProfit =
                //                        abstractProfitService.monthProfit(
                //                                todayElectricProfitVo, projectEntity.getId());
                // 所有项目的累计
                if (todayProfitVo != null) {
                    if (todayProfitVo.getTotalBenefit() != null) {
                        result.setTodayBenefit(
                                String.valueOf(
                                        Double.parseDouble(result.getTodayBenefit())
                                                + Double.parseDouble(
                                                        todayProfitVo.getTotalBenefit())));
                    } else {
                        result.setTotalBenefit(
                                String.valueOf(Double.parseDouble(result.getTotalBenefit()) + 0));
                    }
                }
                if (monthProfit != null) {
                    OperationProfitVo operationMonthProfit = (OperationProfitVo) monthProfit;
                    if (operationMonthProfit.getTotalBenefit() != null) {
                        result.setMonthBenefit(
                                String.valueOf(
                                        Double.parseDouble(result.getMonthBenefit())
                                                + Double.parseDouble(
                                                        operationMonthProfit.getTotalBenefit())));
                    } else {
                        result.setMonthBenefit(
                                String.valueOf(Double.parseDouble(result.getMonthBenefit()) + 0));
                    }
                }
                if (totalProfitVO != null) {
                    if (totalProfitVO.getTotal_benefit() != null) {
                        result.setTotalBenefit(
                                String.valueOf(
                                        Double.parseDouble(result.getTotalBenefit())
                                                + Double.parseDouble(
                                                        totalProfitVO.getTotal_benefit())));
                    } else {
                        result.setTotalBenefit(
                                String.valueOf(Double.parseDouble(result.getTotalBenefit()) + 0));
                    }
                }
                if (carbonTotal != null) {
                    result.setCarbonReduction(
                            String.valueOf(
                                    Double.parseDouble(result.getCarbonReduction())
                                            + carbonTotal.getCarbonReduction()));
                }
            } catch (Exception e) {
                log.error(
                        "projectAllProfitCarbon method error projectId:{}, stackTrace:{}",
                        projectEntity.getId(),
                        e.getStackTrace());
            }
        }
        return result;
    }

    /**
     * 获取 当前登录的 用户的 并且开启了云端大屏的项目列表
     *
     * @return : List<ProjectEntity> 绑定到当前用户的并且 开启了云端大屏的项目列表
     */
    private List<ProjectEntity> getUserCloudLargeScreenProjects(CloudLargeScreenSearch search) {
        if (SecurityUtil.getPrincipal() == null) {
            return List.of();
        }
        boolean allProject = SecurityUtil.getPrincipal().getUserEntity().getAllProject();
        if (allProject) {
            // 如果是全部项目的话，就不需要过滤 , 只需要过滤掉 没有开启云端大屏的项目
            if (!StringUtil.isEmpty(search.getProjectModel())) {
                return projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false)
                                .eq(ProjectEntity::getCloudLargeScreen, true)
                                .eq(ProjectEntity::getProjectModel, search.getProjectModel())
                                .eq(
                                        search.getProvince() != null,
                                        ProjectEntity::getProvince,
                                        search.getProvince()));
            } else {
                return projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false)
                                .eq(ProjectEntity::getCloudLargeScreen, true)
                                .eq(
                                        search.getProvince() != null,
                                        ProjectEntity::getProvince,
                                        search.getProvince()));
            }
        } else {
            // 查询当前用户的项目列表, 未对接账号系统之前 是从本地数据库查询, 目前已经改成了 projectServiceKt.getCurrentProjectList
            // 从账号系统拉取资源列表
            // 这里不用考虑 site 场站 没有账号系统 , 因为场站没有 云大屏
            AuthUser authUser = SecurityUtil.getPrincipal();
            assert authUser != null;
            Map<String, String> currentProjects =
                    projectServiceKt.getCurrentProjectList(
                            Objects.requireNonNull(SecurityUtil.getLoginRoleId()),
                            authUser.getAccountSystemSessionId());

            List<String> projectIds = new ArrayList<>();
            if (currentProjects != null) {
                projectIds = new ArrayList<>(currentProjects.keySet());
            }
            if (projectIds.isEmpty()) {
                return new ArrayList<>();
            }
            // 2024-04-30 09:30:58 新增 查询项目类型查询 云端还是本地项目
            if (!StringUtil.isEmpty(search.getProjectModel())) {
                return projectService.list(
                        Wrappers.lambdaQuery(ProjectEntity.class)
                                .eq(ProjectEntity::getWhetherDelete, false)
                                .eq(ProjectEntity::getCloudLargeScreen, true)
                                .in(ProjectEntity::getId, projectIds)
                                .eq(ProjectEntity::getProjectModel, search.getProjectModel())
                                .eq(
                                        search.getProvince() != null,
                                        ProjectEntity::getProvince,
                                        search.getProvince()));
            }

            return projectService.list(
                    Wrappers.lambdaQuery(ProjectEntity.class)
                            .eq(ProjectEntity::getWhetherDelete, false)
                            .eq(ProjectEntity::getCloudLargeScreen, true)
                            .in(ProjectEntity::getId, projectIds)
                            .eq(
                                    search.getProvince() != null,
                                    ProjectEntity::getProvince,
                                    search.getProvince()));
        }
    }

    /**
     * 近7日的 所有项目的充放电 电量收益等信息
     *
     * @return : List<ProjectRecentWeekChargeOverviewVo> 7天的列表数据
     */
    @Override
    public List<ProjectRecentWeekChargeOverviewVo> projectRecentWeekCharge(
            CloudLargeScreenProjectSearch search) {
        CloudLargeScreenSearch search1 = new CloudLargeScreenSearch();
        List<ProjectEntity> list = getUserCloudLargeScreenProjects(search1);
        List<ProjectRecentWeekChargeOverviewVo> result = new LinkedList<>();
        // 2024-01-24 18:58:40 add 近七日充放电概况 增加了 支持 根据项目id查询
        String projectId = search.getProjectId();
        if (!StringUtil.isEmpty(projectId)) {
            list =
                    list.stream()
                            .filter(projectEntity -> projectEntity.getId().equals(projectId))
                            .collect(Collectors.toList());
        }
        Map<String, Boolean> incomeOpenStatusMap =
                projectService.getIncomeOpenStatusByProjectIds(
                        list.stream().map(ProjectEntity::getId).collect(Collectors.toList()));
        for (int i = 1; i <= 7; i++) {
            ProjectRecentWeekChargeOverviewVo projectRecentWeekChargeOverviewVo =
                    new ProjectRecentWeekChargeOverviewVo();
            for (ProjectEntity projectEntity : list) {
                if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                    continue;
                }
                if (Boolean.TRUE.equals(incomeOpenStatusMap.get(projectEntity.getId()))) {
                    // 开启了
                    // 开启收益的时候 去查询 和 收益绑定的 电量信息
                    openProfitCase(projectEntity, i, projectRecentWeekChargeOverviewVo);
                } else {
                    // 1.4.2 add 如果没开启收益 就去查询 只缓存了电量的 缓存数据
                    noOpenProfitCase(projectEntity, i, projectRecentWeekChargeOverviewVo);
                }
            }
            result.add(projectRecentWeekChargeOverviewVo);
        }
        return result;
    }

    /**
     * TODO 重构 不开收益的情况下 去 汇总近七日( 昨天开始往前推7天的)的 充放电
     *
     * @param projectEntity : 不开收益的项目
     * @param i : 最远的第i天 , i= 1 是第一天 7天最远的那天
     * @param projectRecentWeekChargeOverviewVo:汇总的数据对象
     */
    private void noOpenProfitCase(
            ProjectEntity projectEntity,
            int i,
            ProjectRecentWeekChargeOverviewVo projectRecentWeekChargeOverviewVo) {
        // 查询 项目的 i 这个数据
        // 根据i 来得到 时间 i = 1 -> 7 , 1 是第一天 7天最远的那天
        List<MyTimeUtil.DayHolder> oneWeekTimes =
                MyTimeUtil.getOneWeekTime(projectEntity.getTimezone());
        MyTimeUtil.DayHolder dayHolder = oneWeekTimes.get(7 - i);
        // 1.4.2 只查询 ems设备的电量
        List<DayReportEntity> dayReportEntities =
                dayReportService.findAllByRangeEquipmentTypeAndProjectId(
                        projectEntity.getId(),
                        // 只查询 ems设备的电量
                        EquipmentTypeEnums.EMS_DEVICE,
                        new RangeRequest()
                                .setStartDate(dayHolder.getStartZonedDateTime().toEpochSecond())
                                .setEndDate(dayHolder.getEndZonedDateTime().toEpochSecond()));
        // 这里可能有多个ems设备的 当天的合计一下
        dayReportEntities.forEach(
                dayReportEntity -> {
                    double inData =
                            dayReportEntity.getInData() == null ? 0.0 : dayReportEntity.getInData();
                    double outData =
                            dayReportEntity.getOutData() == null
                                    ? 0.0
                                    : dayReportEntity.getOutData();
                    double sumCharge =
                            Double.parseDouble(
                                            projectRecentWeekChargeOverviewVo
                                                    .getTotalChargeQuantity())
                                    + inData;

                    double sumDischarge =
                            Double.parseDouble(
                                            projectRecentWeekChargeOverviewVo
                                                    .getTotalDischargeQuantity())
                                    + outData;
                    projectRecentWeekChargeOverviewVo.setTotalDischargeQuantity(
                            String.valueOf(sumDischarge));
                    projectRecentWeekChargeOverviewVo.setTotalChargeQuantity(
                            String.valueOf(sumCharge));
                });
    }

    /**
     * TODO 重构 <br>
     * 开收益的情况下 去 汇总近七日( 昨天开始往前推7天的)的充放电 <br>
     * 保持和以前逻辑一致 从recentWeek 近7日缓存map里去取
     *
     * @param projectEntity : 不开收益的项目
     * @param i : 最远的第i天 , i= 1 是第一天 7天最远的那天
     * @param projectRecentWeekChargeOverviewVo:汇总的数据对象
     */
    private static void openProfitCase(
            ProjectEntity projectEntity,
            int i,
            ProjectRecentWeekChargeOverviewVo projectRecentWeekChargeOverviewVo) {
        // 这个数据是 index 0 = 最近的一天 , index 6 = 最远的那天
        List<ProfitVO> recentWeekProfits = TaskOperationTimer.recentWeek.get(projectEntity.getId());
        if (recentWeekProfits != null) {
            ProfitVO profitVO;
            // 这里因为i=1代表第一天 近七天最远的那天, 所以取recentWeekProfits 的最大的index
            profitVO = recentWeekProfits.get(7 - i);
            if (profitVO != null) {
                double sumCharge =
                        Double.parseDouble(
                                        projectRecentWeekChargeOverviewVo.getTotalChargeQuantity())
                                + Double.parseDouble(profitVO.getTotal_charge_quantity());
                projectRecentWeekChargeOverviewVo.setTotalChargeQuantity(String.valueOf(sumCharge));

                double sumDischarge =
                        Double.parseDouble(
                                        projectRecentWeekChargeOverviewVo
                                                .getTotalDischargeQuantity())
                                + Double.parseDouble(profitVO.getTotal_discharge_quantity());
                projectRecentWeekChargeOverviewVo.setTotalDischargeQuantity(
                        String.valueOf(sumDischarge));
            }
        }
    }

    /**
     * 查询所有项目的收益和碳减排数据总览
     *
     * @param timePointKey : 时间点的key
     * @return : List<ProjectProfitCarbonOverviewVo> 所有项目 收益和碳减排数据总览的列表数据
     */
    @Override
    public List<ProjectProfitCarbonOverviewVo> projectProfitCarbon(
            CloudLargeScreenSearch search, String timePointKey) {
        List<ProjectProfitCarbonOverviewVo> results = new ArrayList<>();
        TimePointEnum timePointEnum;
        if (!StringUtils.hasLength(timePointKey)) {
            // 默认是查询 Total 合计的
            timePointEnum = TimePointEnum.TOTAL;
        } else {
            timePointEnum =
                    Optional.ofNullable(EmsConstants.TIME_POINT_ENUM_MAP.get(timePointKey))
                            .orElse(TimePointEnum.TOTAL);
        }

        List<ProjectEntity> list = getUserCloudLargeScreenProjects(search);
        // 遍历项目 查询每个项目的 收益和碳排放信息, 用于云大屏轮播k
        for (ProjectEntity projectEntity : list) {
            if (list.size() > 1 && projectEntity.getProjectModel() == 1) {
                continue;
            }
            // 某个项目的 收益和碳排放信息
            ProjectProfitCarbonOverviewVo projectProfitCarbonOverviewVo =
                    this.projectProfitAndCarbon(timePointEnum, projectEntity);
            // 1.3.9 前端要求 提供项目的币种
            projectProfitCarbonOverviewVo.setCurrency(projectEntity.getCurrency());
            results.add(projectProfitCarbonOverviewVo);
        }
        return results;
    }

    /**
     * 所有项目的 项目状态 概览
     *
     * @return : ProjectStatusOverviewVo 项目状态概览vo
     */
    @Override
    public ProjectStatusOverviewVo projectStatus(CloudLargeScreenSearch search) {
        // 所有的项目
        List<ProjectEntity> projectEntities = getUserCloudLargeScreenProjects(search);

        // 根据项目的状态进行分类
        long offLineCount =
                projectEntities.stream()
                        .filter(
                                projectEntity ->
                                        projectEntity
                                                .getStatus()
                                                .equals(EmsStatusEnum.OFFLINE.getStatus()))
                        .count();

        // 告警中的项目 & 故障中的项目 , 排除掉离线的项目
        long alarmFaultCount =
                projectEntities.stream()
                        .filter(
                                projectEntity ->
                                        !projectEntity
                                                        .getStatus()
                                                        .equals(EmsStatusEnum.OFFLINE.getStatus())
                                                && (projectEntity.getAlarm() > 0
                                                        || projectEntity.getFault() > 0))
                        .count();

        Set<Integer> emsRunningStatus =
                new HashSet<>(
                        Arrays.asList(
                                EmsStatusEnum.CHARGING.getStatus(),
                                EmsStatusEnum.DISCHARGING.getStatus(),
                                EmsStatusEnum.STOP.getStatus(),
                                EmsStatusEnum.STANDBY.getStatus()));
        // 运行中的项目 排除掉 (告警中的项目 & 故障中的项目)
        long runningCount =
                projectEntities.stream()
                        .filter(
                                projectEntity ->
                                        emsRunningStatus.contains(projectEntity.getStatus())
                                                && (projectEntity.getAlarm() == 0
                                                        && projectEntity.getFault() == 0))
                        .count();

        return new ProjectStatusOverviewVo()
                .setRunningCount(runningCount)
                .setOffLineCount(offLineCount)
                .setAlarmFaultCount(alarmFaultCount);
    }

    @Override
    public Map<String, List<ValueVO>> projectChargeOneRate(
            RequestWithDeviceId requestWithDeviceId) {
        String projectId = requestWithDeviceId.getProjectId();
        ProjectEntity projectEntity = projectService.getById(projectId);
        RequestWithDeviceId temp = new RequestWithDeviceId();
        BeanUtil.copyProperties(requestWithDeviceId, temp);
        Map<String, List<ValueVO>> rate = new HashMap<>(2);
        if (projectEntity != null) {
            if (projectEntity.getCloudLargeScreen()) {
                FluxRateCommonHolder holder = new FluxRateCommonHolder();
                BeanUtils.copyProperties(requestWithDeviceId, holder);
                if (requestWithDeviceId.getPeriod() != null) {
                    holder.setPeriod(Long.valueOf(requestWithDeviceId.getPeriod()));
                }
                // TODO refactor 看看要不要丢到 newDiagramService 里面
                WebUtils.projectId.set(requestWithDeviceId.getProjectId());
                rate =
                        EquipmentTimeSeriesUtils.rateQueryEngine
                                .getRateAggregationMap(
                                        influxClient.getBucketMean(),
                                        influxClient.getEmsTable(
                                                requestWithDeviceId.getProjectId()),
                                        holder,
                                        List.of(
                                                EmsFieldEnum.EMS_AC_ACTIVE_POWER_POS.field(),
                                                EmsFieldEnum.EMS_AC_ACTIVE_POWER_NEG.field()),
                                        () ->
                                                deviceService.deviceIdsPrepare(
                                                        requestWithDeviceId.getProjectId(),
                                                        requestWithDeviceId.getDeviceId()))
                                .getMap();
            } else {
                throw new ServiceException(ErrorResultCode.CLOUD_LARGESCREEN_NOT_OPEN.value());
            }
        }
        return rate;
    }

    /**
     * 暂时只是给澳洲和欧洲数据中心用的 大屏查询 冲放电量缓存
     *
     * @param search
     * @return
     */
    @Override
    public ProjectAllElectricOverviewVo projectAllElectric(CloudLargeScreenSearch search) {

        // 1. 获取到 当前用户可以看到的项目列表
        List<ProjectEntity> list = getUserCloudLargeScreenProjects(search);
        ProjectAllElectricOverviewVo overviewVo = new ProjectAllElectricOverviewVo();
        if (list.isEmpty()) {
            return overviewVo;
        }
        // 查询今日
        ElectricCollectVo todayElectricCollectVo = new ElectricCollectVo();
        ElectricCollectVo monthElectricCollectVo = new ElectricCollectVo();
        ElectricCollectVo totalElectricCollectVo = new ElectricCollectVo();
        list.forEach(
                project -> {
                    ElectricCollectVo electricCollectVo =
                            TaskOperationTimer.todayElectricCollectMap.get(project.getId());
                    if (electricCollectVo != null) {
                        todayElectricCollectVo.setDischarge(
                                todayElectricCollectVo.getDischarge()
                                        + electricCollectVo.getDischarge());
                        todayElectricCollectVo.setCharge(
                                todayElectricCollectVo.getCharge() + electricCollectVo.getCharge());
                    }
                });

        // 查询当月
        Map<String, List<ProjectEntity>> timezoneProjectGroup =
                list.stream().collect(Collectors.groupingBy(ProjectEntity::getTimezone));
        timezoneProjectGroup.forEach(
                (timezone, timezoneProjects) -> {
                    long currentMonthZeroTime = MyTimeUtil.getCurrentMonthZeroTime(timezone);
                    long currentTime = Instant.now().getEpochSecond();
                    DayReportEntity monthDayReportEntity =
                            dayReportService.findSumByCondition(
                                    timezoneProjects.stream()
                                            .map(ProjectEntity::getId)
                                            .collect(Collectors.toList()),
                                    EquipmentTypeEnums.EMS_DEVICE,
                                    //                                    null,
                                    new RangeRequest()
                                            .setStartDate(currentMonthZeroTime)
                                            .setEndDate(currentTime));
                    if (monthDayReportEntity != null) {
                        monthElectricCollectVo.setDischarge(
                                monthElectricCollectVo.getDischarge()
                                        + monthDayReportEntity.getOutData());
                        monthElectricCollectVo.setCharge(
                                monthElectricCollectVo.getCharge()
                                        + monthDayReportEntity.getInData());
                    }
                });
        // 查询合计
        DayReportEntity totalSumDayReportEntity =
                dayReportService.findSumByCondition(
                        list.stream().map(ProjectEntity::getId).collect(Collectors.toList()),
                        EquipmentTypeEnums.EMS_DEVICE,
                        null);

        if (totalSumDayReportEntity != null) {
            totalElectricCollectVo.setDischarge(
                    totalElectricCollectVo.getDischarge() + totalSumDayReportEntity.getOutData());
            totalElectricCollectVo.setCharge(
                    totalElectricCollectVo.getCharge() + totalSumDayReportEntity.getInData());
        }
        double totalCarbonReduction = 0.0;
        for (ProjectEntity project : list) {
            // 获取CarbonTotal 从缓存中获取
            CarbonTotal carbonTotal = TaskCarbonTimer.carbonTotalMap.get(project.getId());
            // 兼容一下 好像前端 这个是null 就直接列表不展示了
            if (carbonTotal == null) {
                carbonTotal = new CarbonTotal();
            }
            double carbonReduction = carbonTotal.getCarbonReduction();
            totalCarbonReduction += carbonReduction;
        }
        overviewVo
                .setElectricCollectVo(
                        new CacheElectricCollectVo(
                                todayElectricCollectVo,
                                null,
                                monthElectricCollectVo,
                                totalElectricCollectVo))
                .setCarbonReduction(String.valueOf(totalCarbonReduction));
        return overviewVo;
    }

    /**
     * 计算 充放电量
     *
     * @param list : 项目列表
     * @return : Pair<Long, Long> : 充电量,放电量
     */
    private Pair<Double, Double> calculateQuantity(List<ProjectManageVo> list) {
        double sumChargeQuantity = 0.0;
        double sumDischargeQuantity = 0.0;
        List<String> projectIds =
                list.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        Map<String, Boolean> incomeOpenStatusByProjectIds =
                projectService.getIncomeOpenStatusByProjectIds(projectIds);
        for (ProjectManageVo projectManageVo : list) {
            // 开启收益的 走老的数据查询
            if (Boolean.TRUE.equals(incomeOpenStatusByProjectIds.get(projectManageVo.getId()))) {
                // 开了收益 保持以前的逻辑 只是 1.4.2 新重构改了一个 内存缓存service 去查询电量 根据 memoryCacheEnum 类型
                ElectricProfitVO electricCache =
                        incomeMemoryCacheServiceKt.getElectricCache(
                                projectManageVo.getId(), MemoryCacheEnum.TOTAL_CACHE);
                double projectDischarge = electricCache.getTotalDischargeQuantity();
                double projectCharge = electricCache.getTotalChargeQuantity();
                sumDischargeQuantity += projectDischarge;
                sumChargeQuantity += projectCharge;
            } else {
                // 1.4.2 没开收益 则去查询 1.4.2 新的 day report 电量的缓存 去查询ems设备的电量
                // 查询dayReport 数据
                DayReportEntity dayReportEntity =
                        dayReportService.findSumByCondition(
                                List.of(projectManageVo.getId()),
                                // 只查询 ems设备的电量
                                EquipmentTypeEnums.EMS_DEVICE,
                                null);
                // 可能查询不到 有可能这个项目没有 设备or电表就无法缓存所谓的 电量
                if (dayReportEntity != null) {
                    double projectCharge =
                            dayReportEntity.getInData() == null ? 0.0 : dayReportEntity.getInData();
                    double projectDischarge =
                            dayReportEntity.getOutData() == null
                                    ? 0.0
                                    : dayReportEntity.getOutData();
                    sumDischargeQuantity += projectDischarge;
                    sumChargeQuantity += projectCharge;
                }
            }
        }
        return Pair.of(sumChargeQuantity, sumDischargeQuantity);
    }

    // 下面是待删除的 以前的实现,  以前实现里 包括 了今天的电量 , 这个1.4.2 已经改了不需要今天的 7天是指 从昨天 往前推7天的电量数据
    // 获取ElectricProfitVO 由于这里已经缓存了今天的相关数据所以很快
    //            ElectricProfitVO todayElectricProfitVo =
    //
    // TaskOperationTimer.todayElectricMap.get(projectManageVo.getId());
    //            // 复用 totalProfit 方法 内部会从totalElectricMap获取到项目初始到昨天的数据 +
    // todayElectricProfitVo
    //            TotalProfitVO totalProfitVO =
    //                    abstractProfitService.totalProfit(
    //                            todayElectricProfitVo, projectManageVo.getId());
    //            // 充电
    //            String projectTotalChargeQuantity =
    //                    totalProfitVO.getTotal_charge_quantity() == null
    //                            ? "0"
    //                            : totalProfitVO.getTotal_charge_quantity();
    //            // 放电
    //            String projectTotalDisChargeQuantity =
    //                    totalProfitVO.getTotal_discharge_quantity() == null
    //                            ? "0"
    //                            : totalProfitVO.getTotal_discharge_quantity();
    //            sumChargeQuantity += Double.parseDouble(projectTotalChargeQuantity);
    //            sumDischargeQuantity +=
    // Double.parseDouble(projectTotalDisChargeQuantity);

    /**
     * 计算 额定容量和额定功率
     *
     * @param list : 项目列表
     * @return : Pair<Long, Long> : 额定容量,额定功率
     */
    private Pair<Long, Long> calculateCapacityAndPower(List<ProjectManageVo> list) {
        long sumCapacity = 0L;
        long sumPower = 0L;
        // 对开启了云端大屏的项目进行操作
        for (ProjectManageVo projectManageVo : list) {
            String size = projectManageVo.getSize();
            Pair<Long, Long> pair = ParseCapacity.getCapacity(size);
            sumCapacity += pair.getFirst();
            sumPower += pair.getSecond();
        }
        return Pair.of(sumCapacity, sumPower);
    }
}
