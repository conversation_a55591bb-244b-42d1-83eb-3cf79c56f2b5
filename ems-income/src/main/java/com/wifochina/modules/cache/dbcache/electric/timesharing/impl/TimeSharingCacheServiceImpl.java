package com.wifochina.modules.cache.dbcache.electric.timesharing.impl;

import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.util.CacheUtils;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.timesharing.TimeSharingCacheService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.util.OperationUtil;
import com.wifochina.modules.project.entity.ProjectEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 2024/6/26 16:34.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class TimeSharingCacheServiceImpl implements TimeSharingCacheService {

    private final CacheUtils cacheUtils;
    private final ElectricAdapterChooser chooser;

    @Override
    public void timeSharingCacheBattery(
            long timePoint, ProjectEntity projectEntity, ElectricPriceEntity electricPriceEntity) {
        // 分时缓存目前的条件是  要开启分时的开关 并且 是国内项目, 国外项目可能和他自己的收益 会有冲突 就是上面那段 等以后需要再处理吧..
        if (projectEntity.getTimeSharingCache() && cacheUtils.isChina(projectEntity.getCountry())) {
            log.info("project {}  start timesharing cache save", projectEntity.getId());
            // 开启了分时缓存 则存入一份 分时缓存的数据 , 也就是要走 动态这个表
            chooser.choose(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())
                    .saveElectric(
                            new ElectricAdapterService.ElectricAdapterContext() {
                                @Override
                                public long timePoint() {
                                    return timePoint;
                                }

                                @Override
                                public List<ElectricPriceEntity> periodPriceList() {
                                    // 构建24小时的 时间段 需要把电价 填充进去 比如匹配到的电价的尖时间段是 00:00 -> 8:00 电价要填充到
                                    // 00:00 -> 1:00 ... 7:00 ->8:00 这8个小时的 记录里 供计算使用
                                    // 2024-07-12 09:42:18修改成了  00:00 -> 00:30 ... 7:30 -> 8:00
                                    return OperationUtil.getTimeSharingCacheHalfHourPeriods(
                                            electricPriceEntity);
                                }

                                @Override
                                public ProjectEntity project() {
                                    return projectEntity;
                                }
                            });

            log.info("project {}  end timesharing cache save", projectEntity.getId());
        }
    }
}
