package com.wifochina.modules.cache.dbcache.electric.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.IdSearchUtils;
import com.wifochina.common.util.TimeContext;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.ElectricDynamicPeriodService;
import com.wifochina.modules.electric.entity.ElectricDynamicPeriodEntity;
import com.wifochina.modules.electric.mapper.ElectricDynamicPeriodMapper;
import com.wifochina.modules.electric.request.ElectricRequest;
import com.wifochina.modules.group.entity.IdSearchSupport;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.income.caculate.electric.IElectricIncomeCalculateService;
import com.wifochina.modules.operation.VO.ElectricProfitVO;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.aop.framework.AopContext;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Created on 2024/5/24 11:46.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class ElectricDynamicPeriodServiceImpl
        extends ServiceImpl<ElectricDynamicPeriodMapper, ElectricDynamicPeriodEntity>
        implements ElectricAdapterService<ElectricDynamicPeriodEntity>,
                ElectricDynamicPeriodService {

    private final DeviceService deviceService;
    private final AmmeterService ammeterService;
    private final IElectricIncomeCalculateService IElectricIncomeCalculateService;

    @Override
    public List<ElectricDynamicPeriodEntity> queryElectricSumGroupByDeviceId(
            ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricSumGroupByDeviceId(electricRequest);
    }

    @Override
    public List<ElectricDynamicPeriodEntity> queryElectricMonthGroupByDeviceId(
            ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricMonthGroupByDeviceId(electricRequest);
    }

    @Override
    public List<ElectricDynamicPeriodEntity> queryElectricYear(ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricYear(electricRequest);
    }

    /**
     * 1.3.7 分时缓存查询
     *
     * @param electricRequest : request
     * @return list
     */
    @Override
    public List<ElectricDynamicPeriodEntity> queryElectricTimeSharingProfit(
            ElectricRequest electricRequest) {
        return this.baseMapper.queryElectricTimeSharingProfit(electricRequest);
    }

    @Override
    public ElectricProfitVO queryElectricTotalProfit(
            ElectricRequest electricRequest, QueryElectricResult queryElectricResult) {
        return queryElectricResult.dynamicPeriodPostProcessor(
                this.baseMapper.queryElectricTotalProfit(electricRequest));
    }

    @Override
    public void queryElectricEveryDayProfit(
            ElectricRequest electricRequest, QueryElectricsResult queryElectricsResult) {
        queryElectricsResult.dynamicPeriodPostProcessor(
                this.baseMapper.queryElectricEveryDayProfit(electricRequest));
    }

    @Override
    public void deviceElectricSave(ElectricAdapterContext context, TimeContext timeContext) {
        getProxy()
                .recordCommonSave(
                        context,
                        timeContext,
                        deviceService.findIncomeDevices(context.project().getId()),
                        IElectricIncomeCalculateService.getElectricEmsSql(context.project()));
    }

    @Override
    public void electricSaveForDataCalibration(
            ElectricAdapterContext context, TimeContext timeContext) {
        // TODO..
        throw new ServiceException("暂不支持 实时电价的 补值操作");
    }

    @Override
    public void ammeterElectricSave(ElectricAdapterContext context, TimeContext timeContext) {
        getProxy()
                .recordCommonSave(
                        context,
                        timeContext,
                        ammeterService.findIncomeOrReadingAmmeter(context.project().getId()),
                        IElectricIncomeCalculateService.getElectricMeterSql(context.project()));
    }

    @Override
    public void commonPeriodList(
            ElectricAdapterContext context,
            TimeContext timeContext,
            List<ElectricDynamicPeriodEntity> results,
            String sql,
            IdSearchSupport idEntity) {
        // 每个时段 一条记录
        LocalDate time = timeContext.getTimePointDate().toLocalDate();
        for (ElectricPriceEntity price : context.periodPriceList()) {
            Pair<Long, Long> periodStartEndPair =
                    MyTimeUtil.getPeriodStartEndPair(
                            price.getStartTime(),
                            price.getEndTime(),
                            timeContext.getTimePointDate().toEpochSecond(),
                            context.project().getTimezone());
            long periodStartTime = periodStartEndPair.getFirst();
            long periodEndTime = periodStartEndPair.getSecond();
            ElectricDynamicPeriodEntity electricDynamicPeriodEntity =
                    new ElectricDynamicPeriodEntity()
                            .setBuyPrice(price.getBuyPrice())
                            .setSellPrice(price.getSellPrice())
                            .setPeriodStartTime(periodStartTime)
                            .setPeriodEndTime(periodEndTime)
                            .setProjectId(context.project().getId())
                            .setYear(time.getYear())
                            .setMonth(time.getMonthValue())
                            .setDay(time.getDayOfMonth())
                            .setDeviceId(idEntity.id());
            String queryString = sql;
            queryString =
                    queryString.replace(
                            IdSearchUtils.PLACEHOLDER,
                            IdSearchUtils.createIdSearchSqlForTimeSeries(List.of(idEntity)));
            // 根据不同的 时序service去执行 可以走lindorm 和 influx
            try {
                IElectricIncomeCalculateService.calculateTimeChargeDiff(
                        timeContext,
                        price,
                        context.project(),
                        queryString,
                        // 把时序库查询得到的结果 调用calculatePeriod 方法
                        (outDiff, inDiff) ->
                                calculatePeriod(
                                        price, electricDynamicPeriodEntity, outDiff, inDiff));

            } catch (Exception e) {
                log.error("recordCommonSave calculateTimeChargeDiff e:{}", e.getMessage());
            }
            results.add(electricDynamicPeriodEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void recordCommonSave(
            ElectricAdapterContext context,
            TimeContext timeContext,
            List<? extends IdSearchSupport> idSearchSupports,
            String sql) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String projectId = context.project().getId();
        if (!idSearchSupports.isEmpty()) {
            idSearchSupports.forEach(
                    idEntity -> {
                        log.info(
                                "dynamic recordCommonSave timePoint:{} id:{} type:{}",
                                timeContext.getTimePointDate().format(formatter),
                                idEntity.id(),
                                idEntity.type());
                        List<ElectricDynamicPeriodEntity> results = new ArrayList<>();
                        // 每个时段 一条记录
                        LocalDate time = timeContext.getTimePointDate().toLocalDate();
                        // 为了供 查询今天的 抽出来的方法 只获取到 results
                        this.commonPeriodList(context, timeContext, results, sql, idEntity);
                        // 支持重跑  先删除原始缓存数据
                        getProxy()
                                .remove(
                                        new LambdaQueryWrapper<ElectricDynamicPeriodEntity>()
                                                .eq(
                                                        ElectricDynamicPeriodEntity::getProjectId,
                                                        projectId)
                                                .eq(
                                                        ElectricDynamicPeriodEntity::getDeviceId,
                                                        idEntity.id())
                                                .eq(
                                                        ElectricDynamicPeriodEntity::getYear,
                                                        time.getYear())
                                                .eq(
                                                        ElectricDynamicPeriodEntity::getMonth,
                                                        time.getMonthValue())
                                                .eq(
                                                        ElectricDynamicPeriodEntity::getDay,
                                                        time.getDayOfMonth()));
                        // getProxy().remove(ids);
                        // removeBatchByIds(ids);
                        // 动态时段 是一个时段一条记录
                        getProxy().saveBatch(results);
                    });
        }
    }

    private ElectricDynamicPeriodServiceImpl getProxy() {
        return (ElectricDynamicPeriodServiceImpl) AopContext.currentProxy();
    }

    /**
     * 计算 时段 方法 这个是 动态时段的
     *
     * @param price 价格
     * @param electricDynamicPeriodEntity 时段
     * @param outDiff 输出差值
     * @param inDiff 输入差值
     */
    @Override
    public void calculatePeriod(
            ElectricPriceEntity price,
            ElectricDynamicPeriodEntity electricDynamicPeriodEntity,
            Double outDiff,
            Double inDiff) {
        // TODO remove
        // 这里好像没意义 因为每个时段进来可能价格不同 导致最终都是 最后一个时段设置上去了
        electricDynamicPeriodEntity.setBuyPrice(price.getBuyPrice());
        electricDynamicPeriodEntity.setSellPrice(price.getSellPrice());
        electricDynamicPeriodEntity.setChargeQuantity(
                electricDynamicPeriodEntity.getChargeQuantity() + inDiff);
        electricDynamicPeriodEntity.setChargeCost(
                electricDynamicPeriodEntity.getChargeCost() + ((price.getBuyPrice() * inDiff)));
        electricDynamicPeriodEntity.setDischargeQuantity(
                electricDynamicPeriodEntity.getDischargeQuantity() + outDiff);
        electricDynamicPeriodEntity.setDischargeBenefit(
                electricDynamicPeriodEntity.getDischargeBenefit()
                        + (price.getSellPrice() * outDiff));
        // 最后的收益在这里算 这个收益是 收益-成本的 (毛)
        electricDynamicPeriodEntity.setTotalBenefit(
                electricDynamicPeriodEntity.getDischargeBenefit()
                        - electricDynamicPeriodEntity.getChargeCost());
    }

    @Override
    public void calculatePeriodWithElectricProfitVo(
            ElectricPriceEntity price,
            ElectricProfitVO electricProfitVO,
            Double outDiff,
            Double inDiff) {
        electricProfitVO.setTotalChargeQuantity(electricProfitVO.getTotalChargeQuantity() + inDiff);
        electricProfitVO.setTotalChargeCost(
                electricProfitVO.getTotalChargeCost() + (price.getBuyPrice() * inDiff));

        electricProfitVO.setTotalDischargeQuantity(
                electricProfitVO.getTotalDischargeQuantity() + outDiff);
        electricProfitVO.setTotalDischargeBenefit(
                electricProfitVO.getTotalDischargeBenefit() + (price.getSellPrice() * outDiff));
    }

    @Override
    public Set<String> type() {
        // 固定电价和实时电价 都支持
        return Set.of(
                ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.getValue(),
                ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.getValue());
    }
}
