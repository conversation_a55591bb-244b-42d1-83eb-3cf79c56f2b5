package com.wifochina.modules.cache.dbcache.job;

import cn.hutool.extra.spring.SpringUtil;

import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.electric.ElectricAdapterChooser;
import com.wifochina.modules.electric.ElectricAdapterService;
import com.wifochina.modules.electric.timesharing.TimeSharingCacheService;
import com.wifochina.modules.operation.entity.ElectricPriceEntity;
import com.wifochina.modules.operation.service.ElectricPriceService;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @since 2024-03-14 2:02 PM
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class YestBatteryProfitJob extends QuartzJobBean {

    private final ElectricAdapterChooser chooser;

    private final TimeSharingCacheService timeSharingCacheService;

    private final ProjectService projectService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        String timezone = (String) context.getJobDetail().getJobDataMap().get("timezone");
        List<ProjectEntity> projectEntities = projectService.getProjectsByTimeZone(timezone);
        for (ProjectEntity projectEntity : projectEntities) {
            try {
                log.info("yestBatteryProfitJob start");
                saveOneDayElectric(projectEntity);
                log.info("yestBatteryProfitJob end");
            } catch (Exception e) {
                log.error("项目 {} 保存昨日电量收益异常 {}", projectEntity.getProjectName(), e.getMessage());
            }
        }
    }

    public void saveOneDayElectric(ProjectEntity projectEntity) {
        String projectId = projectEntity.getId();
        long yesterdayZero =
                MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone())
                        - EmsConstants.ONE_DAY_SECOND;
        ElectricPriceService electricPriceService = SpringUtil.getBean(ElectricPriceService.class);
        // 找到 那个匹配到的 电价配置策略
        ElectricPriceEntity electricPriceEntity =
                electricPriceService.getElectricPriceMatch(projectId, yesterdayZero);
        if (electricPriceEntity != null) {
            // 通过 chooser去选择 一个 执行 适配尖峰平谷 or 新的 自定义时段
            chooser.choose(projectEntity.getElectricPriceType())
                    .saveElectric(
                            new ElectricAdapterService.ElectricAdapterContext() {
                                @Override
                                public long timePoint() {
                                    return yesterdayZero;
                                }

                                @Override
                                public List<ElectricPriceEntity> periodPriceList() {
                                    return electricPriceEntity.getPeriodPriceList();
                                }

                                @Override
                                public ProjectEntity project() {
                                    return projectEntity;
                                }
                            });

            // 1.3.7 add 分时缓存;
            timeSharingCacheService.timeSharingCacheBattery(
                    yesterdayZero, projectEntity, electricPriceEntity);
        } else {
            log.error(
                    "can`t find electricPrice by projectId:{} , yesterdayZero: {}",
                    projectId,
                    yesterdayZero);
        }
    }
}
