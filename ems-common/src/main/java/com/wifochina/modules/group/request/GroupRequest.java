package com.wifochina.modules.group.request;

import com.wifochina.common.util.DemandControlEnum;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分组
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
public class GroupRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组id")
    private String id;

    @ApiModelProperty(value = "分组名称")
    private String name;

    @ApiModelProperty(value = "计算收益开关(false关闭)(true打开）")
    private Boolean calcEarningsController;

    @ApiModelProperty(value = "风电收益开关(false关闭)(true打开）")
    private Boolean windEarningsController;

    @ApiModelProperty(value = "余热发电收益开关(false关闭)(true打开）")
    private Boolean wasterEarningsController;

    @ApiModelProperty(value = "分组储能收益开关(false关闭)(true打开）")
    private Boolean groupEarningsController;

    @ApiModelProperty(value = "控制需量开关(false关闭)(true打开）")
    private Boolean demandController;

    /**
     * 1.4.0 新增需量控制 整合了以前的老的2个 demandController + demandIncome字段
     *
     * @see DemandControlEnum
     */
    @ApiModelProperty(value = "控制需量整合下拉框('不启用', '启用,显示需量收益', '启用,隐藏需量收益' )")
    private String demandControl;

    @ApiModelProperty(value = "需量控制模式 1负载开环控制模式 2电网闭环控制模式 3混合控制模式")
    private Integer demandControllerModel;

    @ApiModelProperty(value = "PV(光伏)模式开关(false关闭)(true打开）")
    private Boolean photovoltaicController;

    @ApiModelProperty(value = "PV(光伏)收益开关开关(false关闭)(true打开）")
    private Boolean pvProfitController;

    @ApiModelProperty(value = "DCDC(光伏)收益开关开关(false关闭)(true打开）")
    private Boolean dcdcProfitController;

    @ApiModelProperty(value = "光伏发电模式 1全额上网 2自发自用余量上网")
    private Integer photovoltaicModel;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "关联的电表id")
    private List<String> ammeterIds;

    @ApiModelProperty(value = "关联的设备id")
    private List<String> deviceIds;

    @ApiModelProperty(value = "关联的可控设备id")
    private List<String> controllableIds;

    @ApiModelProperty(value = "是否开启防逆流")
    private Boolean antiReflux;

    @ApiModelProperty(value = "是否开启soc界限")
    private Boolean enableStopSoc;

    @ApiModelProperty(value = "是否接入load")
    private Boolean enableLoad;

    @ApiModelProperty(value = "是否接入load电表")
    private Boolean enableLoadGrid;

    @ApiModelProperty(value = "是否展示天气数据:false 不展示,true展示")
    private Boolean enableWeather;

    @ApiModelProperty(value = "是否开启光伏预测:0 不预测, 1 预测")
    private Boolean pvPrediction;

    @ApiModelProperty(value = "是否开启负载预测:0 不预测, 1 预测")
    private Boolean loadPrediction;

    @ApiModelProperty(value = "10000-20000之间,隔100的整数值")
    private Integer externalControllerOffset;

    @ApiModelProperty(value = "是否接入储能设备EMS")
    private Boolean enableEms;

    @ApiModelProperty(value = "是否接入并网电表")
    private Boolean enableElectricGrid;

    @ApiModelProperty(value = "是否显示电网电量,默认展示")
    private Boolean enableShowElectricQuantity;

    @ApiModelProperty(value = "pcs型号")
    private String pcsCode;

    @ApiModelProperty(value = "是否接入风电")
    private Boolean enableWindPowerGeneration;

    @ApiModelProperty(value = "风电发电模式 1全额上网 2自发自用余量上网")
    private Integer windPowerModel;

    @ApiModelProperty(value = "余热发电模式 1全额上网 2自发自用余量上网(时段电价), 3 自发自用(协议电价)")
    private Integer wasterPowerModel;

    @ApiModelProperty(value = "是否接入柴发")
    private Boolean enableWoodPowerGeneration;

    @ApiModelProperty(value = "是否接入充电桩")
    private Boolean enableChargingPilePower;

    @ApiModelProperty(value = "是否接入燃气发电机")
    private Boolean enableGasPowerGeneration;

    @ApiModelProperty(value = "外部控制器(false关闭)(true打开）")
    private Boolean externalController;

    @ApiModelProperty(value = "外部控制模式切换：是否根据远端控制自动切换，false为手动")
    private Boolean directPowerAutoControl;

    @ApiModelProperty(value = "外部控制模式自动时，切换开关的104遥控点位")
    private Integer directPowerControlIec104EnableIoa;

    @ApiModelProperty(value = "外部控制模式下，104协议中，公共地址")
    private Integer directPowerControlIec104CommonAddr;

    @ApiModelProperty(value = "外部控制模式下，104协议中，有功功率摇调地址")
    private Integer directPowerControlIec104ActiveIoa;

    @ApiModelProperty(value = "外部控制模式下，104协议中，无功功率摇调地址")
    private Integer directPowerControlIec104ReactiveIoa;

    @ApiModelProperty(value = "modbus协议中，有功功率寄存器地址(两个寄存器)")
    private Integer directPowerControlModbusActiveAddr;

    @ApiModelProperty(value = "modbus协议中，无功功率寄存器地址(两个寄存器)")
    private Integer directPowerControlModbusReactiveAddr;

    @ApiModelProperty(value = "是否有消防")
    private Boolean hasFireFighting;

    @ApiModelProperty(value = "数据展示方式 0代表ems, 1代表电表")
    private Boolean showType;

    @ApiModelProperty(value = "是否开启电池显示")
    private Boolean showBatteryElectricity;

    @ApiModelProperty(value = "需量收益(false关闭)(true打开）")
    private Boolean demandIncome;

    @ApiModelProperty(value = "需量告警阈值(超出多少就告警)")
    private Double demandAlarmThreshold;

    @ApiModelProperty(value = "需量控制系数(0-200)")
    private Double demandControlRate;

    @ApiModelProperty(value = "需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗")
    // @ApiModelProperty(value = "需量计算模式:1固定时段15分钟;2固定时段30分钟;3一分钟滑窗")
    // 1.4.0改成了这样 只有2个 , 默认是 滑差
    // 这里要把老数据进行更新一下
    private Integer demandCalcModel;

    @ApiModelProperty(value = "滑差时间")
    private Integer slipTime;

    @ApiModelProperty(value = "demand周期")
    private Integer demandPeriod;

    @ApiModelProperty(value = "当前控制需量调整模式 , manual, auto_calc, auto_meter")
    // @see DemandControlAdjustModelEnum
    private String demandControlAdjustModel;

    @ApiModelProperty(value = "需量自动调整比例(%) , 自动抬升的时候需要")
    private Double demandControlAutoRate;

    @ApiModelProperty(value = "需量自动调整上线(kW)")
    private Double demandControlAutoUpLimit;

    @ApiModelProperty(value = "需量提醒开关")
    private Boolean demandRemindController;

    @ApiModelProperty(value = "容量控制开关(false关闭)(true打开）")
    private Boolean capacityController;

    // 1.4.2 add 容量提醒 开关 + 容量告警阈值
    @ApiModelProperty(value = "容量提醒开关")
    private Boolean capacityRemindController;

    @ApiModelProperty(value = "容量告警阈值(超出多少就告警)")
    private Double capacityAlarmThreshold;

    @ApiModelProperty(value = "可控设备运行策略")
    private String controllableStrategies;

    @ApiModelProperty(value = "余热发电开关")
    private Boolean enableWastePowerGeneration;

    @ApiModelProperty(value = "ems soc上限策略")
    private String emsStrategies;

    @ApiModelProperty(value = "avc调度监控开关 默认关闭(false关闭)(true打开）")
    private Boolean avcMonitorController;

    @ApiModelProperty(value = "首页系统配图")
    private String diagramOfSystem;

    /**
     * @see com.wifochina.common.util.LoadPictureEnum
     */
    @ApiModelProperty(value = "负荷配图")
    private String diagramOfLoad;

    @ApiModelProperty(value = "排序")
    private Integer orderIndex;

    @ApiModelProperty(value = "开启 vpp")
    private Boolean openVpp;

    @ApiModelProperty(value = "开启 录波")
    private Boolean waveRecord;

    @ApiModelProperty(value = "是否开启agc开关")
    private Boolean agcController;

    @ApiModelProperty(value = "是否开启avc开关")
    private Boolean avcController;

    @ApiModelProperty(value = "微网储能SOC被动均衡开关")
    // 1.4.2 added  打开后，并网储能功率会根据离网储能的SOC和功率自动调节，达到所有储能SOC的平衡状态
    private Boolean mgSocBalance;

    // 1.4.2 added
    @ApiModelProperty(value = "微网储能SOC被动均衡 功率限制比例0-1 默认0.8")
    private Double mgSocBalanceOffGridPowerLimitRatio;

    @ApiModelProperty(value = "首页显示无功功率开关 (默认关闭)")
    private Boolean homePageReactivePowerController;

    @ApiModelProperty(value = "首页整站/单机零功率(默认开启)")
    private Boolean homePageZeroPowerController;

    @ApiModelProperty(value = "日报数据间隔(5/15/30/60)")
    private Integer dayReportDataInterval;
}
