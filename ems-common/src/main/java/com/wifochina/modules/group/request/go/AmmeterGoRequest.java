package com.wifochina.modules.group.request.go;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * AmmeterGORequest
 *
 * @since 4/20/2022 11:38 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AmmeterGoRequest {

    @ApiModelProperty(value = "电表ip")
    private String host;

    @ApiModelProperty(value = "电表端口")
    private Integer port;

    @ApiModelProperty(value = "协议类型")
    private String protocol;

    @ApiModelProperty(value = "设备名称")
    private String device;

    @ApiModelProperty(value = "波特率")
    @JsonProperty("baud_rate")
    private Integer baudRate;

    @ApiModelProperty(value = "数据位")
    @JsonProperty("data_bits")
    private Integer dataBits;

    @ApiModelProperty(value = "停止位")
    @JsonProperty("stop_bits")
    private Integer stopBits;

    @ApiModelProperty(value = "奇偶校验")
    private String parity;

    @ApiModelProperty(value = "1 PV电表(PV)、2并网点电表(Grid)、3负载电表(Load)")
    private String type;

    @ApiModelProperty(value = "电表型号，厂商名称 只支持 EMSMeter 和 DTSD3125")
    private String vendor;

    @ApiModelProperty(value = "电表标识符uuid")
    private String uuid;

    @ApiModelProperty(value = "是否反接，默认false")
    private Boolean reverse;

    @ApiModelProperty(value = "寻址位置")
    private String aux;

    @ApiModelProperty(value = "CEM9000的测控号，1或者2")
    @JsonProperty("meter_num")
    private Integer meterNum;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "电量输出初始值")
    @JsonProperty("ac_history_positive_initial_power_in_kwh")
    private Double outputHistoryInit;

    @ApiModelProperty(value = "电量输入初始值")
    @JsonProperty("ac_history_negative_initial_power_in_kwh")
    private Double inputHistoryInit;

    @ApiModelProperty(value = "CEM9000是否使用低压")
    @JsonProperty("use_low_side")
    private Boolean useLowSide;

    @ApiModelProperty(value = "高压侧 CT比")
    @JsonProperty("high_ct_ratio")
    private Double highCtRatio;

    @ApiModelProperty(value = "高压侧 PT比")
    @JsonProperty("high_pt_ratio")
    private Double highPtRatio;

    @ApiModelProperty(value = "高压侧保护CT比")
    @JsonProperty("high_protect_ct_ratio")
    private Double highProtectCtRatio;

    @ApiModelProperty(value = "低压侧 CT比")
    @JsonProperty("low_ct_ratio")
    private Double lowCtRatio;

    @ApiModelProperty(value = "低压侧 PT比")
    @JsonProperty("low_pt_ratio")
    private Double lowPtRatio;

    @ApiModelProperty(value = "dlt645设备地址")
    @JsonProperty("dlt645_device_address")
    private String dlt645DeviceAddress;

    @ApiModelProperty(value = "dlt645前置cmd")
    @JsonProperty("dlt645_prefix_cmd")
    private String dlt645PrefixCmd;

    @ApiModelProperty(value = "从机号 默认 1")
    @JsonProperty("slave_id")
    private Integer slaveId;

    /** 1.4.2 added 需要下发name */
    @ApiModelProperty(value = "设备name")
    private String name;

    @ApiModelProperty(value = "isBackup")
    private Boolean isBackup;
}
