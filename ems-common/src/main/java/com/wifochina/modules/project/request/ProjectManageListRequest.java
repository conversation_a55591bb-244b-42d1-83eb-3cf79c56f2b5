package com.wifochina.modules.project.request;

import com.wifochina.common.page.PageBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * @since 9/2/2022 11:06 AM
 * <AUTHOR>
 * @version 1.0
 */
@Data
@ApiModel(value = "管理端-项目查询条件")
public class ProjectManageListRequest extends PageBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目模式 1场站模式 0云模式")
    private Integer projectModel;

    @ApiModelProperty(value = "项目状态")
    private Set<Integer> status;

    @ApiModelProperty(value = "告警状态  1 正常 2 有故障 3有报警 4 有故障和报警")
    private Integer alertStatus;

    @ApiModelProperty(value = "所在省份")
    private String province;

    @ApiModelProperty(value = "项目id")
    private Set<String> projectIds;

    @ApiModelProperty(value = "电价托管")
    private Boolean priceProxy;

    @ApiModelProperty(value = "是否维护")
    private Boolean maintain;

    @ApiModelProperty(value = "资方id")
    private String investor;

    @ApiModelProperty(value = "是否按照屏蔽后的故障排名")
    private Boolean orderShield;

    @ApiModelProperty(value = "按照规则排序：0 故障在前(默认)，1 正常在前 , 2 createTime降序 3 createTime升序")
    private Integer orderRule = 0;

    @ApiModelProperty(value = "控容：1、控需：2、其他：0")
    private Integer controlType;

    @ApiModelProperty(value = "投运开始时间")
    private Long startTime;

    @ApiModelProperty(value = "投运结束时间")
    private Long endTime;
}
