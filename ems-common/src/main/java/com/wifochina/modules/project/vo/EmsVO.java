package com.wifochina.modules.project.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SiteVO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 5/7/2022 5:01 PM
 */
@Data
public class EmsVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "ems充电功率")
    private Double emsInPower;

    @ApiModelProperty(value = "ems名称")
    private String name;

    @ApiModelProperty(value = "ems日充电量")
    private Double emsInDaily;

    @ApiModelProperty(value = "ems充电总量")
    private Double emsInTotal;

    @ApiModelProperty(value = "ems放电功率")
    private Double emsOutPower;

    // 2025-06-05 17:19:05 added for ems 无功
    @ApiModelProperty(value = "ems无功冲功率")
    private Double emsAcReactiveInPower;

    @ApiModelProperty(value = "ems无功放功率")
    private Double emsAcReactiveOutPower;


    // ----------

    @ApiModelProperty(value = "ems日放电量")
    private Double emsOutDaily;

    @ApiModelProperty(value = "ems放电总量")
    private Double emsOutTotal;

    @ApiModelProperty(value = "ems状态 0离线 1待机 2充电 3放电")
    private Integer emsStatus;

    @ApiModelProperty(value = "soc值")
    private Double soc;

    @ApiModelProperty(value = "GPS显示开关 0不显示，1正常值, 2未取到值")
    private Integer showGps;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "维度")
    private Double latitude;

    @ApiModelProperty(value = "是否维护,默认不维护false")
    private Boolean maintain;

    @ApiModelProperty(value = "最后更新时间")
    private Long updateTime;

    @ApiModelProperty(value = "设备序列号")
    private String emsSerial;

    @ApiModelProperty(value = "电站额定功率")
    private Double emsDesignPower;

    @ApiModelProperty(value = "最大可充功率")
    private Double emsChargePower;

    @ApiModelProperty(value = "最大可放功率")
    private Double emsDischargePower;

    @ApiModelProperty(value = "电站额定容量")
    private Double emsCapacity;

    @ApiModelProperty(value = "交流功率")
    private Double diffPower;

    @ApiModelProperty(value = "无功功率")
    private Double diffAcReactivePower;

    @ApiModelProperty(value = "dcdc的Pv功率")
    private Double dcdcTotalPv;

    @ApiModelProperty(value = "dcdc的光伏总发电量")
    private Double dcdcOutTotal;

    @ApiModelProperty(value = "dcdc的光伏日发电量")
    private Double dcdcDailyOutTotal;

    // 这两个 通过计算 pcs交流-dcdcTotalPv 得到的

    @ApiModelProperty(value = "dcdc电池放电功率")
    private Double dcdcBatteryOutPower;

    @ApiModelProperty(value = "dcdc电池充电功率")
    private Double dcdcBatteryInPower;

    // 这两个直接拿 ems的值 读取的点位

    @ApiModelProperty(value = "dcdc的电池总放电量")
    private Double dcdcBatteryOutTotal;

    @ApiModelProperty(value = "dcdc的电池总充电量")
    private Double dcdcBatteryInTotal;

    // 这两个取的 ems的 也是从数据库查询的

    @ApiModelProperty(value = "dcdc的电池日放电量")
    private Double dcdcBatteryDailyOutSum;

    @ApiModelProperty(value = "dcdc的电池日充电量")
    private Double dcdcBatteryDailyInSum;

    @ApiModelProperty(value = "是否有Dcdc")
    private Boolean hasDcdc;

    @ApiModelProperty(value = "是否主机")
    private Boolean isHost;

    @ApiModelProperty(value = "协议版本号")
    private Integer protocol;

    @ApiModelProperty(value = "是否外部控制 0否，大于0 是")
    private Integer outControl;

    @ApiModelProperty(value = "是否SN冲突 0不冲突，1冲突")
    private int snConflict = 0;
}
