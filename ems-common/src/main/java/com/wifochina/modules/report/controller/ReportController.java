package com.wifochina.modules.report.controller;

import com.wifochina.common.page.Result;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.report.VO.AmmeterReportVo;
import com.wifochina.modules.report.VO.ReadDetailReport;
import com.wifochina.modules.report.VO.ReadReport;
import com.wifochina.modules.report.request.ReadingReportRequest;
import com.wifochina.modules.report.request.ReportRequest;
import com.wifochina.modules.report.service.ReportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@RequestMapping("/report")
@RestController
@Api(tags = "15-报表")
@RequiredArgsConstructor
public class ReportController {

    private final ReportService reportService;

    /** 电站日报 */
    @PostMapping("/daily")
    @ApiOperation("日报")
    @PreAuthorize("hasAuthority('/report/daily')")
    public Result<Map<String, Object>> listDailyReport(@RequestBody ReportRequest reportRequest) {
        reportRequest.setProjectId(WebUtils.projectId.get());
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> map = reportService.getReport(reportRequest, EmsConstants.DAILY);
        return Result.success(map);
    }

    /** 电站月报 */
    @PostMapping("/month")
    @ApiOperation("月报")
    @PreAuthorize("hasAuthority('/report/month')")
    public Result<Map<String, Object>> listMonthReport(@RequestBody ReportRequest reportRequest) {
        reportRequest.setProjectId(WebUtils.projectId.get());
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> reportNew =
                reportService.getWholeStationReport(reportRequest, EmsConstants.MONTH);
        //        Map<String, Object> map = reportService.getReport(reportRequest,
        // EmsConstants.MONTH);
        return Result.success(reportNew);
    }

    /** 电站年报 */
    @PostMapping("/year")
    @ApiOperation("年报")
    @PreAuthorize("hasAuthority('/report/year')")
    public Result<Map<String, Object>> listYearReport(@RequestBody ReportRequest reportRequest) {
        reportRequest.setProjectId(WebUtils.projectId.get());
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        //        Map<String, Object> map = reportService.getReport(reportRequest,
        // EmsConstants.YEAR);
        Map<String, Object> reportNew =
                reportService.getWholeStationReport(reportRequest, EmsConstants.YEAR);
        return Result.success(reportNew);
    }

    /** 电站日报 */
    @PostMapping("/meter/daily")
    @ApiOperation("电表日报")
    @PreAuthorize("hasAuthority('/report/meter/daily')")
    public Result<Map<String, Object>> listMeterDailyReport(
            @RequestBody ReportRequest reportRequest) {
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        Map<String, Object> map = reportService.getMeterReport(reportRequest, EmsConstants.DAILY);
        return Result.success(map);
    }

    /** 电站月报 */
    @PostMapping("/meter/month")
    @ApiOperation("电表月报")
    @PreAuthorize("hasAuthority('/report/meter/month')")
    public Result<Map<String, List<AmmeterReportVo>>> lisMonthReport(
            @RequestBody ReportRequest reportRequest) {
        reportRequest.setProjectId(WebUtils.projectId.get());
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        //        Map<String, Object> map = reportService.getMeterReport(reportRequest,
        // EmsConstants.MONTH);
        Map<String, List<AmmeterReportVo>> meterReportNew =
                reportService.getMeterReportNew(reportRequest, EmsConstants.MONTH);
        //        return Result.success(map);
        return Result.success(meterReportNew);
    }

    /** 电站年报 */
    @PostMapping("/meter/year")
    @ApiOperation("电表年报")
    @PreAuthorize("hasAuthority('/report/meter/year')")
    public Result<Map<String, List<AmmeterReportVo>>> lisYearReport(
            @RequestBody ReportRequest reportRequest) {
        reportRequest.setProjectId(WebUtils.projectId.get());
        reportRequest.setEndDate(
                Math.min(reportRequest.getEndDate(), Instant.now().getEpochSecond()));
        //        Map<String, Object> map = reportService.getMeterReport(reportRequest,
        // EmsConstants.YEAR);
        Map<String, List<AmmeterReportVo>> meterReportYearNew =
                reportService.getMeterReportNew(reportRequest, EmsConstants.YEAR);
        return Result.success(meterReportYearNew);
    }

    /** 抄表月报 */
    @PostMapping("/reading/month")
    @ApiOperation("抄表月报")
    @PreAuthorize("hasAuthority('/report/reading/month')")
    public Result<Map<String, ReadReport>> readingMonth(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadReport> map =
                reportService.getReadMonth(
                        readingReportRequest.getStartDate(),
                        readingReportRequest.getMeterId(),
                        WebUtils.projectId.get());
        return Result.success(map);
    }

    /** 抄表月报详情 */
    @PostMapping("/reading/month/detail")
    @ApiOperation("抄表月报详情")
    @PreAuthorize("hasAuthority('/report/reading/month')")
    public Result<Map<String, ReadDetailReport>> readingMonthDetail(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadDetailReport> map =
                reportService.getReadMonthDetail(
                        readingReportRequest.getStartDate(),
                        readingReportRequest.getMeterId(),
                        WebUtils.projectId.get());
        return Result.success(map);
    }

    /** 抄表年报 */
    @PostMapping("/reading/year")
    @ApiOperation("抄表年报")
    @PreAuthorize("hasAuthority('/report/reading/year')")
    public Result<Map<String, ReadReport>> readingYear(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadReport> map =
                reportService.getReadYear(
                        readingReportRequest.getStartDate(),
                        readingReportRequest.getMeterId(),
                        WebUtils.projectId.get());
        return Result.success(map);
    }

    /** 抄表月报详情 */
    @PostMapping("/reading/year/detail")
    @ApiOperation("抄表年报详情")
    @PreAuthorize("hasAuthority('/report/reading/year')")
    public Result<Map<String, ReadDetailReport>> readingYearDetail(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadDetailReport> map =
                reportService.getReadYearDetail(
                        readingReportRequest.getStartDate(),
                        readingReportRequest.getMeterId(),
                        WebUtils.projectId.get());
        return Result.success(map);
    }

    /** 电表抄表月报 */
    @PostMapping("/readingMeter/month")
    @ApiOperation("电表抄表月报")
    @PreAuthorize("hasAuthority('/report/readingMeter/month')")
    public Result<Map<String, ReadReport>> readingMeterMonth(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadReport> map =
                reportService.getReadMeter(
                        readingReportRequest.getStartDate(),
                        new ReportService.MeterHolder() {
                            @Override
                            public String meterId() {
                                return readingReportRequest.getMeterId();
                            }

                            @Override
                            public String period() {
                                return EmsConstants.MONTH;
                            }

                            @Override
                            public String projectId() {
                                return WebUtils.projectId.get();
                            }
                        });
        return Result.success(map);
    }

    /** 电表抄表月报详情 */
    @PostMapping("/readingMeter/month/detail")
    @ApiOperation("电表抄表月报详情")
    @PreAuthorize("hasAuthority('/report/readingMeter/month')")
    public Result<Map<String, ReadDetailReport>> readingMeterMonthDetail(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadDetailReport> map =
                reportService.getReadMeterDetail(
                        readingReportRequest.getStartDate(),
                        new ReportService.MeterHolder() {
                            @Override
                            public String meterId() {
                                return readingReportRequest.getMeterId();
                            }

                            @Override
                            public String period() {
                                return EmsConstants.MONTH;
                            }

                            @Override
                            public String projectId() {
                                return WebUtils.projectId.get();
                            }
                        });
        return Result.success(map);
    }

    /** 电表抄表年报 */
    @PostMapping("/readingMeter/year")
    @ApiOperation("电表抄表年报")
    @PreAuthorize("hasAuthority('/report/readingMeter/year')")
    public Result<Map<String, ReadReport>> readingMeterYear(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadReport> map =
                reportService.getReadMeter(
                        readingReportRequest.getStartDate(),
                        new ReportService.MeterHolder() {
                            @Override
                            public String meterId() {
                                return readingReportRequest.getMeterId();
                            }

                            @Override
                            public String period() {
                                return EmsConstants.YEAR;
                            }

                            @Override
                            public String projectId() {
                                return WebUtils.projectId.get();
                            }
                        });
        return Result.success(map);
    }

    /** 电表抄表年报详情 */
    @PostMapping("/readingMeter/year/detail")
    @ApiOperation("电表抄表年报详情")
    @PreAuthorize("hasAuthority('/report/readingMeter/year')")
    public Result<Map<String, ReadDetailReport>> readingMeterYearDetail(
            @RequestBody ReadingReportRequest readingReportRequest) {
        Map<String, ReadDetailReport> map =
                reportService.getReadMeterDetail(
                        readingReportRequest.getStartDate(),
                        new ReportService.MeterHolder() {
                            @Override
                            public String meterId() {
                                return readingReportRequest.getMeterId();
                            }

                            @Override
                            public String period() {
                                return EmsConstants.YEAR;
                            }

                            @Override
                            public String projectId() {
                                return WebUtils.projectId.get();
                            }
                        });
        return Result.success(map);
    }
}
