package com.wifochina.modules.report.service.impl;

import static java.util.Comparator.comparing;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.influxdb.query.dsl.Flux;
import com.influxdb.query.dsl.functions.restriction.Restrictions;
import com.wifochina.common.constants.EquipmentTypeEnums;
import com.wifochina.common.constants.MeterFieldEnum;
import com.wifochina.common.constants.MeterTypeEnum;
import com.wifochina.common.request.RangeRequest;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.EventLevelEnum;
import com.wifochina.common.util.RemediesDataTypeEnum;
import com.wifochina.modules.client.InfluxClientService;
import com.wifochina.modules.common.search.EquipmentTimeSeriesUtils;
import com.wifochina.modules.diagram.request.FluxRateCommonHolder;
import com.wifochina.modules.event.service.EventMessageService;
import com.wifochina.modules.group.entity.AmmeterEntity;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.service.AmmeterService;
import com.wifochina.modules.group.service.DeviceService;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.report.VO.*;
import com.wifochina.modules.report.entity.DayReportEntity;
import com.wifochina.modules.report.entity.MeterDataRemediesEntity;
import com.wifochina.modules.report.entity.ReportDataRemediesEntity;
import com.wifochina.modules.report.request.ReportRequest;
import com.wifochina.modules.report.service.*;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 4/14/2022 11:25 AM
 */
@Service
@Slf4j
public class NewInfluxReportServiceImpl extends AbstractReportService implements ReportService {

    @Resource private EventMessageService eventMessageService;

    @Resource private InfluxClientService influxClientService;

    @Resource private AmmeterService ammeterService;

    @Resource private DeviceService deviceService;

    @Resource private GroupService groupService;

    @Resource private ReportDataRemediesService reportDataRemediesService;

    @Resource private MeterDataRemediesService meterDataRemediesService;

    @Resource private DayReportService dayReportService;

    @Override
    public Map<String, Object> getWholeStationReport(ReportRequest reportRequest, String type) {
        String projectId = reportRequest.getProjectId();
        RangeRequest rangeRequest =
                new RangeRequest()
                        .setStartDate(reportRequest.getStartDate())
                        .setEndDate(reportRequest.getEndDate());
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 根据range 获取到初始的 reports 代表的是 应该显示的 每天/或者 每个月 的列表
        List<Report> reports = getInitReports(reportRequest, type, projectEntity);
        Map<Long, Report> reportMap =
                reports.stream().collect(Collectors.toMap(Report::getTime, v -> v));

        List<DayReportEntity> aggregatedReports = new ArrayList<>();
        if (type.equals(EmsConstants.MONTH)) {
            // 把日报聚合 按照每天
            aggregatedReports = dayReportService.findDayAggregatedReports(projectId, rangeRequest);
        } else if (type.equals(EmsConstants.YEAR)) {
            // 把日报聚合 按照月 进行汇总,  这里可能有问题 时区问题
            aggregatedReports =
                    dayReportService.findMonthlyAggregatedReports(projectId, rangeRequest);
        }
        // key:time 可以是day的零点时间 or month月份的第一天的零点时间
        Map<Long, List<DayReportEntity>> timeDayReportMaps =
                aggregatedReports.stream()
                        .filter(dayReportEntity -> reportMap.get(dayReportEntity.getTime()) != null)
                        .collect(Collectors.groupingBy(DayReportEntity::getTime));

        // 注意 这里是查询的 整站的补值..  meter report new方法查询的是 只是电表的补值
        Map<String, Map<Long, Double>> reportRemediesDataMapByDataType =
                reportDataRemediesService.getReportRemediesDataMapByDataType(
                        projectId, reportRequest.getStartDate(), type);
        timeDayReportMaps.forEach(
                (time, ammeterReportVos) -> {
                    Report report = reportMap.get(time);
                    for (DayReportEntity ammeterReportVo : ammeterReportVos) {
                        EquipmentTypeEnums typeEnum =
                                EquipmentTypeEnums.valueOf(ammeterReportVo.getEquipmentType());
                        Double outData = ammeterReportVo.getOutData();
                        Double inData = ammeterReportVo.getInData();
                        // 补值逻辑暂时不变 没时间改
                        Map<Long, Double> remediesOutMap =
                                reportRemediesDataMapByDataType.get(
                                        typeEnum.name()
                                                + EmsConstants.UNDER_LINE
                                                + EmsConstants.OUT);
                        Map<Long, Double> remediesInMap =
                                reportRemediesDataMapByDataType.get(
                                        typeEnum.name()
                                                + EmsConstants.UNDER_LINE
                                                + EmsConstants.IN);
                        if (remediesOutMap != null && remediesOutMap.get(time) != null) {
                            outData = remediesOutMap.get(time);
                        }
                        if (remediesInMap != null && remediesInMap.get(time) != null) {
                            inData = remediesInMap.get(time);
                        }
                        switch (typeEnum) {
                            case PV_METER:
                                report.setPvPower(report.getPvPower() + outData);
                                break;
                            case WIND_METER:
                                report.setWindPower(outData);
                                break;
                            case GAS_METER:
                                report.setGasPower(outData);
                                break;
                            case DIESEL_METER:
                                report.setDieselPower(outData);
                                break;
                            case WASTER_METER:
                                report.setWasterPower(outData);
                                break;
                            case GRID_METER:
                                report.setGridOutPower(outData);
                                report.setGridInPower(inData);
                                break;
                            case PILE_METER:
                                report.setPilePower(outData);
                                break;
                            case EMS_DEVICE:
                                report.setEmsOutPower(outData);
                                report.setEmsInPower(inData);
                                // dcdc处理 如果有dcdc 需要把dcdc的 加到 pv 上面
                                Double outEmsDcdcData = ammeterReportVo.getOutEmsDcdcData();
                                report.setPvPower(report.getPvPower() + outEmsDcdcData);
                                break;
                        }
                    }
                    // 处理load 计算
                    calcLoad(report);
                });
        // 处理各种total 合计的
        Map<String, Object> resultMap = calcTotal(reports);
        resultMap.put(
                EmsConstants.ALARM,
                eventMessageService.getErrorOrAlarmNum(
                        projectId, rangeRequest, EventLevelEnum.ALARM));
        resultMap.put(
                EmsConstants.FAULT,
                eventMessageService.getErrorOrAlarmNum(
                        projectId, rangeRequest, EventLevelEnum.FAULT));

        // 处理一下 新增的 容量告警的
        // 1.开启了容量告警的分组
        // 2.查询数量
        // 容量告警不放在这边了 , 保持和需量的一致
        return resultMap;
    }

    private static @NotNull List<Report> getInitReports(
            ReportRequest reportRequest, String type, ProjectEntity projectEntity) {
        List<Report> reports = new ArrayList<>();
        // 1.得到 查询时间范围内 的时间列表
        List<Long> reasonTimeStamps = new ArrayList<>();
        if (type.equals(EmsConstants.MONTH)) {
            reasonTimeStamps =
                    MyTimeUtil.getDailyMidnightTimestamps(
                            ZoneId.of(projectEntity.getTimezone()),
                            reportRequest.getStartDate(),
                            reportRequest.getEndDate());
        } else if (type.equals(EmsConstants.YEAR)) {
            reasonTimeStamps =
                    MyTimeUtil.getMonthlyMidnightTimestamps(
                            ZoneId.of(projectEntity.getTimezone()),
                            reportRequest.getStartDate(),
                            reportRequest.getEndDate());
        }
        for (Long timestamp : reasonTimeStamps) {
            Report report = new Report();
            report.setTime(timestamp);
            reports.add(report);
        }
        return reports;
    }

    /**
     * 根据 list 去循环得到 相关 total 数据 保持和原重构一样的
     *
     * @param reports : reports
     * @return : 数据结构暂且不变
     */
    private Map<String, Object> calcTotal(List<Report> reports) {
        Map<String, Object> map = new HashMap<>(14);
        double totalPv = 0d;
        double totalEmsIn = 0d;
        double totalEmsOut = 0d;
        double totalGridOut = 0d;
        double totalGridIn = 0d;
        double totalLoad = 0d;
        double totalWind = 0d;
        double totalDiesel = 0d;
        double totalGas = 0d;
        double totalWaster = 0d;
        double totalPile = 0d;
        for (Report report : reports) {
            totalPv += report.getPvPower();
            totalWind += report.getWindPower();
            totalGas += report.getGasPower();
            totalDiesel += report.getDieselPower();
            totalWaster += report.getWasterPower();
            totalEmsOut += report.getEmsOutPower();
            totalEmsIn += report.getEmsInPower();
            totalGridOut += report.getGridOutPower();
            totalGridIn += report.getGridInPower();
            totalLoad += report.getLoadPower();
        }
        map.put("reports", reports);
        map.put("total_pv", totalPv);
        map.put("total_ems_in", totalEmsIn);
        map.put("total_ems_out", totalEmsOut);
        map.put("total_grid_out", totalGridOut);
        map.put("total_grid_in", totalGridIn);
        map.put("total_load", totalLoad);
        map.put("total_wind", totalWind);
        map.put("total_pile", totalPile);
        map.put("total_diesel", totalDiesel);
        map.put("total_gas", totalGas);
        map.put("total_waster", totalWaster);

        return map;
    }

    /**
     * 计算 load 负载的 , 保持原来的计算公式
     *
     * @param report : reportJk
     */
    private void calcLoad(Report report) {
        double loadPower =
                report.getEmsOutPower()
                        + report.getPvPower()
                        + report.getGridOutPower()
                        - report.getGridInPower()
                        - report.getEmsInPower()
                        + report.getWindPower()
                        + report.getDieselPower()
                        + report.getGasPower()
                        - report.getPilePower()
                        + report.getWasterPower();
        report.setLoadPower(loadPower);
    }

    /**
     * 报表，
     *
     * @param type type代表类型。daily 日报；month 月报；年报year
     * @param reportRequest request
     * @return 总收益情况
     */
    @Override
    public Map<String, Object> getReport(ReportRequest reportRequest, String type) {
        String projectId = WebUtils.projectId.get();
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        FluxRateCommonHolder holder =
                getFluxRateCommonHolder(systemGroupEntity, reportRequest, type);
        List<Report> reports = new ArrayList<>();
        double totalPv = 0d;
        double totalEmsIn = 0d;
        double totalEmsOut = 0d;
        double totalGridOut = 0d;
        double totalGridIn = 0d;
        double totalLoad = 0d;
        double totalWind = 0d;
        double totalDiesel = 0d;
        double totalGas = 0d;
        double totalWaster = 0d;
        double totalPile = 0d;

        RangeRequest rangeRequest =
                new RangeRequest()
                        .setStartDate(reportRequest.getStartDate())
                        .setEndDate(reportRequest.getEndDate());
        long alarm =
                eventMessageService.getErrorOrAlarmNum(
                        projectId, rangeRequest, EventLevelEnum.ALARM);
        long fault =
                eventMessageService.getErrorOrAlarmNum(
                        projectId, rangeRequest, EventLevelEnum.FAULT);
        //        long alarm = getErrorOrAlarmNum(reportRequest, EventLevelEnum.ALARM.getLevel());
        //        long fault = getErrorOrAlarmNum(reportRequest, EventLevelEnum.FAULT.getLevel());

        // 创建异步任务获取各种数据
        CompletableFuture<Map<Long, Double>> pvMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.PV.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);

                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> gridInMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.GRID.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> gridOutMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.GRID.meterType(),
                                        "ac_history_negative_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> windMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.WIND.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> dieselMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.DIESEL.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> pileMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.PILE.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> gasMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.GAS.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> wasterMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getMeterDifferenceMap(
                                        projectId,
                                        MeterTypeEnum.WASTER.meterType(),
                                        "ac_history_positive_power_in_kwh",
                                        systemGroupEntity.getId(),
                                        EmsConstants.ALL,
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> emsInMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getDeviceDifferenceMap(
                                        projectId,
                                        systemGroupEntity.getId(),
                                        "ems_history_input_energy",
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> emsOutMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getDeviceDifferenceMap(
                                        projectId,
                                        systemGroupEntity.getId(),
                                        "ems_history_output_energy",
                                        holder);
                            } finally {
                                WebUtils.projectId.remove();
                            }
                        });

        CompletableFuture<Map<Long, Double>> dcdcMapFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            try {
                                WebUtils.projectId.set(projectId);
                                return getDeviceDifferenceMap(
                                        projectId,
                                        systemGroupEntity.getId(),
                                        "dcdc_meter_history_energy_pos",
                                        holder);
                            } finally {

                                WebUtils.projectId.remove();
                            }
                        });

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(
                        pvMapFuture,
                        gridInMapFuture,
                        gridOutMapFuture,
                        windMapFuture,
                        dieselMapFuture,
                        pileMapFuture,
                        gasMapFuture,
                        wasterMapFuture,
                        emsInMapFuture,
                        emsOutMapFuture,
                        dcdcMapFuture);

        try {
            // 等待所有任务完成
            allFutures.get();

            // 获取所有结果
            Map<Long, Double> pvMap = pvMapFuture.get();
            Map<Long, Double> gridInMap = gridInMapFuture.get();
            Map<Long, Double> gridOutMap = gridOutMapFuture.get();
            Map<Long, Double> windMap = windMapFuture.get();
            Map<Long, Double> dieselMap = dieselMapFuture.get();
            Map<Long, Double> pileMap = pileMapFuture.get();
            Map<Long, Double> gasMap = gasMapFuture.get();
            Map<Long, Double> wasterMap = wasterMapFuture.get();
            Map<Long, Double> emsInMap = emsInMapFuture.get();
            Map<Long, Double> emsOutMap = emsOutMapFuture.get();
            Map<Long, Double> dcdcMap = dcdcMapFuture.get();

            log.info("所有异步数据获取任务已完成，开始处理数据");

            // EMS净出 电池放电电量 - 电池充电电量
            // 电网净出 电网取电 - 馈网电量
            // 总消耗 = 电网馈 + pv发电 +EMS放 - ems充- grid馈
            List<Map<Long, Double>> maps =
                    Arrays.asList(gridOutMap, emsInMap, windMap, dieselMap, pileMap, gasMap);
            Optional<Map<Long, Double>> optionalKeyMap =
                    maps.stream().filter(map -> !map.isEmpty()).findFirst();
            Map<Long, Double> keyMap = optionalKeyMap.orElseGet(Collections::emptyMap);
            List<ReportDataRemediesEntity> reportDataRemediesEntities =
                    reportDataRemediesService.getReportRemediesData(
                            projectId, reportRequest.getStartDate(), type);
            Map<Long, Double> remendiesPvMap =
                    getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.PV.getType());
            Map<Long, Double> remendiesGridOutMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.GRIDOUT.getType());
            Map<Long, Double> remendiesGridInMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.GRIDIN.getType());
            Map<Long, Double> remendiesEmsOutMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.EMSOUT.getType());
            Map<Long, Double> remendiesEmsInMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.EMSIN.getType());
            Map<Long, Double> remendiesWindMap =
                    getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.WIND.getType());
            Map<Long, Double> remendiesDieselMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.DIESEL.getType());
            Map<Long, Double> remendiesGasMap =
                    getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.GAS.getType());
            Map<Long, Double> remendiesWaterMap =
                    getRemediesMap(
                            reportDataRemediesEntities, RemediesDataTypeEnum.WASTER.getType());
            Map<Long, Double> remendiesPileMap =
                    getRemediesMap(reportDataRemediesEntities, RemediesDataTypeEnum.PILE.getType());

            for (Long time : keyMap.keySet()) {
                Report report = new Report();
                report.setTime(time);
                double emsInPower = 0;
                double emsOutPower = 0;
                if (!emsInMap.isEmpty()) {
                    emsInPower = emsInMap.get(time) == null ? 0 : emsInMap.get(time);
                }
                if (remendiesEmsInMap.get(time) != null) {
                    emsInPower = remendiesEmsInMap.get(time);
                }
                report.setEmsInPower(emsInPower);
                totalEmsIn += emsInPower;
                if (!emsOutMap.isEmpty()) {
                    emsOutPower = emsOutMap.get(time) == null ? 0 : emsOutMap.get(time);
                }
                if (remendiesEmsOutMap.get(time) != null) {
                    emsOutPower = remendiesEmsOutMap.get(time);
                }
                report.setEmsOutPower(emsOutPower);
                totalEmsOut += emsOutPower;
                double pvPower = 0;
                if (!pvMap.isEmpty()) {
                    pvPower = pvMap.get(time) == null ? 0 : pvMap.get(time);
                }
                if (remendiesPvMap.get(time) != null) {
                    pvPower = remendiesPvMap.get(time);
                }
                totalPv += pvPower;
                double dcdcPower;
                if (!dcdcMap.isEmpty() && time != null) {
                    dcdcPower = dcdcMap.get(time) == null ? 0 : dcdcMap.get(time);
                    // 把dcdc的pv 添加到 总光伏上
                    totalPv += dcdcPower;
                    // 把dcdc的pv 添加到 光伏上, 后期可能会拆开
                    pvPower += dcdcPower;
                }
                report.setPvPower(pvPower);
                double gridInPower = 0;
                double gridOutPower = 0;
                if (!gridInMap.isEmpty()) {
                    gridInPower = gridInMap.get(time) == null ? 0 : gridInMap.get(time);
                }
                if (remendiesGridInMap.get(time) != null) {
                    gridInPower = remendiesGridInMap.get(time);
                }
                report.setGridInPower(gridInPower);
                totalGridIn += gridInPower;
                if (!gridOutMap.isEmpty()) {
                    gridOutPower = gridOutMap.get(time) == null ? 0 : gridOutMap.get(time);
                }
                if (remendiesGridOutMap.get(time) != null) {
                    gridOutPower = remendiesGridOutMap.get(time);
                }
                report.setGridOutPower(gridOutPower);
                totalGridOut += gridOutPower;
                double windPower = 0;
                if (!windMap.isEmpty()) {
                    windPower = windMap.get(time) == null ? 0 : windMap.get(time);
                }
                if (remendiesWindMap.get(time) != null) {
                    windPower = remendiesWindMap.get(time);
                }
                report.setWindPower(windPower);
                totalWind += windPower;
                double dieselPower = 0;
                if (!dieselMap.isEmpty()) {
                    dieselPower = dieselMap.get(time) == null ? 0 : dieselMap.get(time);
                }
                if (remendiesDieselMap.get(time) != null) {
                    dieselPower = remendiesDieselMap.get(time);
                }
                report.setDieselPower(dieselPower);
                totalDiesel += dieselPower;
                double gasPower = 0;
                if (!gasMap.isEmpty()) {
                    gasPower = gasMap.get(time) == null ? 0 : gasMap.get(time);
                }
                if (remendiesGasMap.get(time) != null) {
                    gasPower = remendiesGasMap.get(time);
                }
                report.setGasPower(gasPower);
                totalGas += gasPower;
                double wasterPower = 0;
                if (!wasterMap.isEmpty()) {
                    wasterPower = wasterMap.get(time) == null ? 0 : wasterMap.get(time);
                }
                if (remendiesWaterMap.get(time) != null) {
                    wasterPower = remendiesWaterMap.get(time);
                }
                report.setWasterPower(wasterPower);
                totalWaster += wasterPower;
                double pilePower = 0;
                if (!pileMap.isEmpty()) {
                    pilePower = pileMap.get(time) == null ? 0 : pileMap.get(time);
                }
                if (remendiesPileMap.get(time) != null) {
                    pilePower = remendiesPileMap.get(time);
                }
                report.setPilePower(pilePower);
                totalPile += pilePower;
                double loadPower =
                        emsOutPower
                                + pvPower
                                + gridOutPower
                                - gridInPower
                                - emsInPower
                                + windPower
                                + dieselPower
                                + gasPower
                                - pilePower
                                + wasterPower;
                totalLoad += loadPower;
                report.setLoadPower(loadPower);
                reports.add(report);
            }
            reports.sort(comparing(Report::getTime));
            Map<String, Object> map = new HashMap<>(14);
            map.put("reports", reports);
            map.put("total_pv", totalPv);
            map.put("total_ems_in", totalEmsIn);
            map.put("total_ems_out", totalEmsOut);
            map.put("total_grid_out", totalGridOut);
            map.put("total_grid_in", totalGridIn);
            map.put("total_load", totalLoad);
            map.put("total_wind", totalWind);
            map.put("total_pile", totalPile);
            map.put("total_diesel", totalDiesel);
            map.put("total_gas", totalGas);
            map.put("total_waster", totalWaster);
            map.put("alarm", alarm);
            map.put("fault", fault);
            return map;

        } catch (Exception e) {
            log.error("异步获取报表数据时发生异常", e);
            throw new RuntimeException("获取报表数据失败", e);
        }
    }

    private static @NotNull Map<Long, Double> getRemediesMap(
            List<ReportDataRemediesEntity> reportDataRemediesEntities, String type) {
        return reportDataRemediesEntities.stream()
                .filter(e -> e.getDataType().equals(type))
                .collect(
                        Collectors.toMap(
                                ReportDataRemediesEntity::getTime,
                                ReportDataRemediesEntity::getData));
    }

    private Map<Long, Double> getMeterDifferenceMap(
            String projectId,
            Integer type,
            String column,
            String groupId,
            String meterId,
            FluxRateCommonHolder holder) {
        return EquipmentTimeSeriesUtils.rateQueryEngine.getDifferenceMap(
                influxClientService.getBucketForever(),
                influxClientService.getMeterTable(projectId),
                holder,
                List.of(column),
                () ->
                        ammeterService.findMeterIdsByGroupIdAndItemIdAndType(
                                projectId, groupId, meterId, type));
    }

    private Map<Long, Double> getDeviceDifferenceMap(
            String projectId, String groupId, String column, FluxRateCommonHolder holder) {
        return EquipmentTimeSeriesUtils.rateQueryEngine.getDifferenceMap(
                influxClientService.getBucketForever(),
                influxClientService.getEmsTable(projectId),
                holder,
                List.of(column),
                () -> deviceService.getGroupDeviceIdList(groupId, EmsConstants.ALL, false));
    }

    private FluxRateCommonHolder getFluxRateCommonHolder(
            GroupEntity systemGroupEntity, ReportRequest reportRequest, String type) {
        FluxRateCommonHolder holder = new FluxRateCommonHolder();
        Integer dayReportDataInterval = systemGroupEntity.getDayReportDataInterval();
        holder.setPeriod(1L);
        reportRequest.setEndDate(reportRequest.getEndDate() + 1);
        if (Objects.equals(type, EmsConstants.DAILY)) {
            if (dayReportDataInterval != null && dayReportDataInterval != 0) {
                if (dayReportDataInterval < 60) {
                    holder.setPeriod(dayReportDataInterval.longValue());
                    holder.setChronoUnit(ChronoUnit.MINUTES);
                    reportRequest.setEndDate(
                            reportRequest.getEndDate()
                                    + ChronoUnit.MINUTES.getDuration().getSeconds()
                                            * dayReportDataInterval);
                }
                if (dayReportDataInterval == 60) {
                    holder.setPeriod(1L);
                    holder.setChronoUnit(ChronoUnit.HOURS);
                    reportRequest.setEndDate(
                            reportRequest.getEndDate()
                                    + ChronoUnit.HOURS.getDuration().getSeconds());
                }
            } else {
                holder.setChronoUnit(ChronoUnit.HOURS);
                reportRequest.setEndDate(
                        reportRequest.getEndDate() + ChronoUnit.HOURS.getDuration().getSeconds());
            }
        } else if (Objects.equals(type, EmsConstants.MONTH)) {
            holder.setChronoUnit(ChronoUnit.DAYS);
            reportRequest.setEndDate(
                    reportRequest.getEndDate() + ChronoUnit.DAYS.getDuration().getSeconds());
        } else if (Objects.equals(type, EmsConstants.YEAR)) {
            holder.setChronoUnit(ChronoUnit.MONTHS);
            reportRequest.setEndDate(
                    reportRequest.getEndDate() + 31 * ChronoUnit.DAYS.getDuration().getSeconds());
        }
        holder.setStartDate(reportRequest.getStartDate());
        holder.setEndDate(reportRequest.getEndDate());
        return holder;
    }

    @Override
    public Map<String, List<AmmeterReportVo>> getMeterReportNew(
            ReportRequest reportRequest, String type) {
        Map<String, List<AmmeterReportVo>> resultMap = new HashMap<>(7);
        String projectId = reportRequest.getProjectId();
        Assert.notNull(projectId, "projectId 不能为空");
        RangeRequest rangeRequest =
                new RangeRequest()
                        .setStartDate(reportRequest.getStartDate())
                        .setEndDate(reportRequest.getEndDate());
        // 获取到 报表相关的补值 记录
        List<MeterDataRemediesEntity> meterDataRemediesEntities =
                meterDataRemediesService.getMeterRemediesData(
                        projectId, reportRequest.getStartDate(), type);
        // 1.获取到所有的电表
        Map<String, AmmeterEntity> ammeterEntityMap =
                ammeterService.findAllMeters(reportRequest.getProjectId()).stream()
                        .collect(Collectors.toMap(AmmeterEntity::getId, v -> v));
        // 2.数据库查询出 查询时间范围内的  数据的汇总
        List<DayReportEntity> dayReportEntities = new ArrayList<>();
        if (type.equals(EmsConstants.MONTH)) {
            // 通俗理解 就是 如果是月报 就要按照 查询的月份展出 1天1天的数据
            dayReportEntities =
                    dayReportService.findAllByRangeAndProjectId(projectId, rangeRequest);
        } else if (type.equals(EmsConstants.YEAR)) {
            // 如果是 年报 每个电表的 每个月度的聚合  所以方法名称上有 聚合的是 带聚合的
            dayReportEntities =
                    dayReportService.findEveryEquipmentMonthlyAggregatedReports(
                            projectId, rangeRequest);
        }
        // 先按照 电表类型 进行大分类
        Map<String, List<DayReportEntity>> dayReportTypeGroupMap =
                dayReportEntities.stream()
                        .filter(
                                // 电表的报表 不需要 ems_device 所以先过滤了
                                dayReportEntity ->
                                        !dayReportEntity
                                                        .getEquipmentType()
                                                        .equals(
                                                                EquipmentTypeEnums.EMS_DEVICE
                                                                        .name())
                                                && ammeterEntityMap.get(
                                                                dayReportEntity.getEquipmentId())
                                                        != null)
                        .collect(
                                Collectors.groupingBy(
                                        DayReportEntity::getEquipmentType, Collectors.toList()));
        // 对于每个 电表分类 进行处理
        dayReportTypeGroupMap.forEach(
                (equipmentType, typeDayReportEntityList) -> {
                    List<AmmeterReportVo> ammeterReportVos = new ArrayList<>();

                    // 对每个分类下面 按照 id 进行分类, 能够得到这个 电表大分类 下面有几个电表
                    Map<String, List<DayReportEntity>> equipmentMaps =
                            typeDayReportEntityList.stream()
                                    .collect(
                                            Collectors.groupingBy(DayReportEntity::getEquipmentId));
                    // 处理每个 大分类下的 具体的电表
                    equipmentMaps.forEach(
                            (equipmentId, timeDayReports) -> {
                                // 这个timeDayReports 可以是 日的时间 也可以是每个月的月初第一天的时间 根据查询的月报还是年报会有变化
                                // 按照时间进行分类 每个具体时间 是一条记录, 日肯定是如此,
                                // 月因为前面findMonthlyAggregatedEquipmentReports已经汇总了 也是如此
                                Map<Long, DayReportEntity> collect =
                                        timeDayReports.stream()
                                                .sorted(
                                                        Comparator.comparingLong(
                                                                DayReportEntity
                                                                        ::getTime)) // 按时间升序排序
                                                .collect(
                                                        Collectors.toMap(
                                                                DayReportEntity::getTime,
                                                                Function.identity(),
                                                                (existing, replacement) ->
                                                                        replacement, // 处理 key 冲突
                                                                LinkedHashMap::new // 保证有序
                                                                ));
                                collect =
                                        getReplacedMapNew(
                                                meterDataRemediesEntities, collect, equipmentId);
                                // 一个电表 一个AmmeterReportVo 对象 , 关于这个电表的 具体的 电表日/月报数据在 collect里
                                ammeterReportVos.add(
                                        new AmmeterReportVo()
                                                .setAmmeterBaseVo(
                                                        new AmmeterBaseVo()
                                                                .setAmmeterId(equipmentId)
                                                                .setAmmeterName(
                                                                        ammeterEntityMap
                                                                                .get(equipmentId)
                                                                                .getName()))
                                                .setDayReports(collect));
                            });
                    resultMap.put(equipmentType, ammeterReportVos);
                });
        return resultMap;
    }

    @Override
    public Map<String, Object> getMeterReport(ReportRequest reportRequest, String type) {
        String projectId = WebUtils.projectId.get();
        GroupEntity systemGroupEntity = groupService.systemGroupEntity(projectId);
        List<AmmeterEntity> list =
                ammeterService.list(
                        Wrappers.lambdaQuery(AmmeterEntity.class)
                                .eq(AmmeterEntity::getProjectId, WebUtils.projectId.get())
                                .orderByAsc(AmmeterEntity::getOrderIndex)
                                .orderByAsc(AmmeterEntity::getCreateTime));
        List<Map<String, Object>> pvList = new ArrayList<>();
        List<Map<String, Object>> gridList = new ArrayList<>();
        List<Map<String, Object>> loadList = new ArrayList<>();
        List<Map<String, Object>> windList = new ArrayList<>();
        List<Map<String, Object>> pileList = new ArrayList<>();
        List<Map<String, Object>> dieselList = new ArrayList<>();
        List<Map<String, Object>> commonList = new ArrayList<>();
        List<Map<String, Object>> gasList = new ArrayList<>();
        List<Map<String, Object>> emsMeterList = new ArrayList<>();
        List<Map<String, Object>> wasterList = new ArrayList<>();
        FluxRateCommonHolder holder =
                getFluxRateCommonHolder(systemGroupEntity, reportRequest, type);
        List<MeterDataRemediesEntity> meterDataRemediesEntities =
                meterDataRemediesService.getMeterRemediesData(
                        projectId, reportRequest.getStartDate(), type);
        for (AmmeterEntity ammeterEntity : list) {
            switch (ammeterEntity.getType()) {
                    // 1PV电表;2并网点电表;3负载电表;4风电;5柴油;6充电桩;7通用;8燃气;9储能
                case 1:
                    // 1PV电表
                    Map<String, Object> pvMap = new HashMap<>(3);
                    Map<Long, Double> pvDataMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.PV.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    pvDataMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    pvDataMap,
                                    ammeterEntity.getId(),
                                    "out");
                    pvMap.put("out", pvDataMap);
                    pvMap.put("name", ammeterEntity.getName());
                    pvMap.put("id", ammeterEntity.getId());
                    pvList.add(pvMap);
                    break;
                case 2:
                    // 2并网点电表
                    Map<String, Object> gridMap = new HashMap<>(4);
                    Map<Long, Double> gridInMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.GRID.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    gridInMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    gridInMap,
                                    ammeterEntity.getId(),
                                    "in");
                    Map<Long, Double> remidiesGridInMap =
                            getMeterRemediesMap(
                                    meterDataRemediesEntities, ammeterEntity.getId(), "in");
                    replaceRemediesMap(gridInMap, remidiesGridInMap);
                    Map<Long, Double> gridOutMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.GRID.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    gridOutMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    gridOutMap,
                                    ammeterEntity.getId(),
                                    "out");
                    gridMap.put("in", gridInMap);
                    gridMap.put("out", gridOutMap);
                    gridMap.put("name", ammeterEntity.getName());
                    gridMap.put("id", ammeterEntity.getId());
                    gridList.add(gridMap);
                    break;
                case 3:
                    // 3负载电表
                    Map<String, Object> loadMap = new HashMap<>(4);
                    Map<Long, Double> loadInMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.LOAD.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    loadInMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    loadInMap,
                                    ammeterEntity.getId(),
                                    "in");
                    Map<Long, Double> loadOutMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.LOAD.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    loadOutMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    loadOutMap,
                                    ammeterEntity.getId(),
                                    "out");
                    loadMap.put("in", loadInMap);
                    loadMap.put("out", loadOutMap);
                    loadMap.put("name", ammeterEntity.getName());
                    loadMap.put("id", ammeterEntity.getId());
                    loadList.add(loadMap);
                    break;
                case 4:
                    // 4风电
                    Map<String, Object> windMap = new HashMap<>(3);
                    Map<Long, Double> dataWindMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.WIND.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    dataWindMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    dataWindMap,
                                    ammeterEntity.getId(),
                                    "out");
                    windMap.put("out", dataWindMap);
                    windMap.put("name", ammeterEntity.getName());
                    windMap.put("id", ammeterEntity.getId());
                    windList.add(windMap);
                    break;
                case 5:
                    // 5柴油
                    Map<String, Object> dieselMap = new HashMap<>(3);
                    Map<Long, Double> dieselDataMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.DIESEL.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    dieselDataMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    dieselDataMap,
                                    ammeterEntity.getId(),
                                    "out");
                    dieselMap.put("out", dieselDataMap);
                    dieselMap.put("name", ammeterEntity.getName());
                    dieselMap.put("id", ammeterEntity.getId());
                    dieselList.add(dieselMap);
                    break;
                case 6:
                    // 6充电桩
                    Map<String, Object> pileMap = new HashMap<>(4);
                    Map<Long, Double> inMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.PILE.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    inMap =
                            getReplacedMap(
                                    meterDataRemediesEntities, inMap, ammeterEntity.getId(), "in");
                    Map<Long, Double> outMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.PILE.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    outMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    outMap,
                                    ammeterEntity.getId(),
                                    "out");
                    pileMap.put("in", inMap);
                    pileMap.put("out", outMap);
                    pileMap.put("name", ammeterEntity.getName());
                    pileMap.put("id", ammeterEntity.getId());
                    pileList.add(pileMap);
                    break;
                case 7:
                    // 7通用
                    Map<String, Object> commonMap = new HashMap<>(4);
                    Map<Long, Double> inCommonMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.COMMON.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    inCommonMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    inCommonMap,
                                    ammeterEntity.getId(),
                                    "in");
                    Map<Long, Double> outCommonMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.COMMON.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    outCommonMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    outCommonMap,
                                    ammeterEntity.getId(),
                                    "out");
                    commonMap.put("in", inCommonMap);
                    commonMap.put("out", outCommonMap);
                    commonMap.put("name", ammeterEntity.getName());
                    commonMap.put("id", ammeterEntity.getId());
                    commonList.add(commonMap);
                    break;
                case 8:
                    // 8燃气
                    Map<String, Object> gasMap = new HashMap<>(4);
                    Map<Long, Double> gasDataMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.GAS.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    gasDataMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    gasDataMap,
                                    ammeterEntity.getId(),
                                    "out");
                    gasMap.put("out", gasDataMap);
                    gasMap.put("name", ammeterEntity.getName());
                    gasMap.put("id", ammeterEntity.getId());
                    gasList.add(gasMap);
                    break;
                case 9:
                    // 9储能
                    Map<String, Object> emsMeterMap = new HashMap<>(4);
                    Map<Long, Double> emsMeterInMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.EMS.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    emsMeterInMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    emsMeterInMap,
                                    ammeterEntity.getId(),
                                    "in");
                    Map<Long, Double> emsMeterOutMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.EMS.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    emsMeterOutMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    emsMeterOutMap,
                                    ammeterEntity.getId(),
                                    "out");
                    emsMeterMap.put("in", emsMeterInMap);
                    emsMeterMap.put("out", emsMeterOutMap);
                    emsMeterMap.put("name", ammeterEntity.getName());
                    emsMeterMap.put("id", ammeterEntity.getId());
                    emsMeterList.add(emsMeterMap);
                    break;
                case 10:
                    // 10 余热发电
                    Map<String, Object> wasterMeterMap = new HashMap<>(4);
                    Map<Long, Double> wasterMeterOutMap =
                            getMeterDifferenceMap(
                                    projectId,
                                    MeterTypeEnum.WASTER.meterType(),
                                    Boolean.TRUE.equals(ammeterEntity.getDcMeter())
                                            ? MeterFieldEnum.DC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field()
                                            : MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                    .field(),
                                    EmsConstants.ALL,
                                    ammeterEntity.getId(),
                                    holder);
                    wasterMeterOutMap =
                            getReplacedMap(
                                    meterDataRemediesEntities,
                                    wasterMeterOutMap,
                                    ammeterEntity.getId(),
                                    "out");
                    wasterMeterMap.put("out", wasterMeterOutMap);
                    wasterMeterMap.put("name", ammeterEntity.getName());
                    wasterMeterMap.put("id", ammeterEntity.getId());
                    wasterList.add(wasterMeterMap);
                    break;
                default:
            }
        }

        Map<String, Object> map = new HashMap<>(7);
        if (!pvList.isEmpty()) {
            map.put("1", pvList);
        }
        if (!gridList.isEmpty()) {
            map.put("2", gridList);
        }
        if (!loadList.isEmpty()) {
            map.put("3", loadList);
        }
        if (!windList.isEmpty()) {
            map.put("4", windList);
        }
        if (!dieselList.isEmpty()) {
            map.put("5", dieselList);
        }
        if (!pileList.isEmpty()) {
            map.put("6", pileList);
        }
        if (!commonList.isEmpty()) {
            map.put("7", commonList);
        }
        if (!gasList.isEmpty()) {
            map.put("8", gasList);
        }
        if (!emsMeterList.isEmpty()) {
            map.put("9", emsMeterList);
        }
        if (!wasterList.isEmpty()) {
            map.put("10", wasterList);
        }
        return map;
    }

    public Map<Long, DayReportEntity> getReplacedMapNew(
            List<MeterDataRemediesEntity> meterDataRemediesEntities,
            Map<Long, DayReportEntity> originMap,
            String meterId) {
        Map<Long, Double> remediesMeterOutMap =
                getMeterRemediesMap(meterDataRemediesEntities, meterId, EmsConstants.OUT);
        Map<Long, Double> remediesMeterInMap =
                getMeterRemediesMap(meterDataRemediesEntities, meterId, EmsConstants.IN);
        replaceRemediesMapNew(EmsConstants.OUT, originMap, remediesMeterOutMap);
        replaceRemediesMapNew(EmsConstants.IN, originMap, remediesMeterInMap);
        return originMap;
    }

    public Map<Long, Double> getReplacedMap(
            List<MeterDataRemediesEntity> meterDataRemediesEntities,
            Map<Long, Double> originMap,
            String meterId,
            String dataType) {
        Map<Long, Double> remediesMeterMap =
                getMeterRemediesMap(meterDataRemediesEntities, meterId, dataType);
        replaceRemediesMap(originMap, remediesMeterMap);
        return originMap;
    }

    public Map<Long, Double> getMeterRemediesMap(
            List<MeterDataRemediesEntity> meterDataRemediesEntities,
            String meterId,
            String dataType) {
        return meterDataRemediesEntities.stream()
                .filter(e -> meterId.equals(e.getMeterId()) && dataType.equals(e.getDataType()))
                .collect(
                        Collectors.toMap(
                                MeterDataRemediesEntity::getTime,
                                MeterDataRemediesEntity::getData));
    }

    public void replaceRemediesMapNew(
            String type, Map<Long, DayReportEntity> originMap, Map<Long, Double> remediesMap) {
        // 遍历 remediesMap
        for (Map.Entry<Long, Double> entry : remediesMap.entrySet()) {
            Long key = entry.getKey();
            Double value = entry.getValue();
            // 如果 originMap 中存在相同的 key，则替换其值
            // 如果不存在相同的 key，则新增键值对
            DayReportEntity dayReportEntity = originMap.get(key);
            if (dayReportEntity != null) {
                //
                if (type.equals("out")) {
                    dayReportEntity.setOutData(value);
                }
                if (type.equals("in")) {
                    dayReportEntity.setInData(value);
                }
                originMap.put(key, dayReportEntity);
            }
        }
    }

    public void replaceRemediesMap(Map<Long, Double> originMap, Map<Long, Double> remediesMap) {
        // 遍历 remediesMap
        for (Map.Entry<Long, Double> entry : remediesMap.entrySet()) {
            Long key = entry.getKey();
            Double value = entry.getValue();
            // 如果 originMap 中存在相同的 key，则替换其值
            // 如果不存在相同的 key，则新增键值对
            originMap.put(key, value);
        }
    }

    @Override
    public List<ReadMonth> getReadingMeter(long start, long end, MeterHolder meterHolder) {
        ProjectEntity projectEntity = projectService.getById(meterHolder.projectId());
        List<ReadMonth> list = new ArrayList<>();
        Flux queryString =
                Flux.from(influxClientService.getBucketForever())
                        .range(start, end)
                        .filter(
                                Restrictions.and(
                                        Restrictions.measurement()
                                                .equal(
                                                        influxClientService.getMeterTable(
                                                                meterHolder.projectId())),
                                        Restrictions.tag("projectId")
                                                .equal(meterHolder.projectId()),
                                        Restrictions.tag(influxClientService.getMeterKey())
                                                .equal(meterHolder.meterId())))
                        .filter(
                                Restrictions.or(
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH
                                                                .field())))
                        .timeShift(
                                MyTimeUtil.getOffSetHourByZoneCode(projectEntity.getTimezone()),
                                ChronoUnit.HOURS)
                        .aggregateWindow(
                                1L,
                                EmsConstants.MONTH.equals(meterHolder.period())
                                        ? ChronoUnit.DAYS
                                        : ChronoUnit.MONTHS,
                                "first")
                        .timeShift(
                                -1L,
                                EmsConstants.MONTH.equals(meterHolder.period())
                                        ? ChronoUnit.DAYS
                                        : ChronoUnit.MONTHS)
                        .timeShift(
                                -MyTimeUtil.getOffSetHourByZoneCode(projectEntity.getTimezone()),
                                ChronoUnit.HOURS)
                        .pivot(new String[] {"_time"}, new String[] {"_field"}, "_value");
        List<FluxTable> tables = influxClientService.getQueryApi().query(queryString.toString());
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Double out =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH.field());
                Double in =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH.field());
                Instant time = (Instant) record.getValueByKey("_time");
                assert time != null;
                ReadMonth readMonth = new ReadMonth();
                readMonth.setTime(time.getEpochSecond());
                readMonth.setChargeReading(in);
                readMonth.setDischargeReading(out);
                list.add(readMonth);
            }
        }
        return list;
    }

    @Override
    public List<ReadMonthDetail> getReadingMeterDetail(
            long start, long end, MeterHolder meterHolder) {
        ProjectEntity projectEntity = projectService.getById(meterHolder.projectId());
        List<ReadMonthDetail> list = new ArrayList<>();
        Flux queryString =
                Flux.from(influxClientService.getBucketForever())
                        .range(start, end)
                        .filter(
                                Restrictions.and(
                                        Restrictions.measurement()
                                                .equal(
                                                        influxClientService.getMeterTable(
                                                                meterHolder.projectId())),
                                        Restrictions.tag("projectId")
                                                .equal(meterHolder.projectId()),
                                        Restrictions.tag(influxClientService.getMeterKey())
                                                .equal(meterHolder.meterId())))
                        .filter(
                                Restrictions.or(
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH_1
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH_2
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH_3
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH_4
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_NEGATIVE_POWER_IN_KWH_5
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH_1
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH_2
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH_3
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH_4
                                                                .field()),
                                        Restrictions.field()
                                                .equal(
                                                        MeterFieldEnum
                                                                .AC_HISTORY_POSITIVE_POWER_IN_KWH_5
                                                                .field())))
                        .timeShift(
                                MyTimeUtil.getOffSetHourByZoneCode(projectEntity.getTimezone()),
                                ChronoUnit.HOURS)
                        .aggregateWindow(
                                1L,
                                EmsConstants.MONTH.equals(meterHolder.period())
                                        ? ChronoUnit.DAYS
                                        : ChronoUnit.MONTHS,
                                "first")
                        .timeShift(
                                -1L,
                                EmsConstants.MONTH.equals(meterHolder.period())
                                        ? ChronoUnit.DAYS
                                        : ChronoUnit.MONTHS)
                        .timeShift(
                                -MyTimeUtil.getOffSetHourByZoneCode(projectEntity.getTimezone()),
                                ChronoUnit.HOURS)
                        .pivot(new String[] {"_time"}, new String[] {"_field"}, "_value");
        List<FluxTable> tables = influxClientService.getQueryApi().query(queryString.toString());
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Double tipOut =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_1.field());
                Double peakOut =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_2.field());
                Double flatOut =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_3.field());
                Double valleyOut =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_4.field());
                Double deepValleyOut =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_POSITIVE_POWER_IN_KWH_5.field());
                Double tipIn =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_1.field());
                Double peakIn =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_2.field());
                Double flatIn =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_3.field());
                Double valleyIn =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_4.field());
                Double deepValleyIn =
                        (Double)
                                record.getValueByKey(
                                        MeterFieldEnum.AC_HISTORY_NEGATIVE_POWER_IN_KWH_5.field());

                Instant time = (Instant) record.getValueByKey("_time");
                assert time != null;
                ReadMonthDetail readMonth = new ReadMonthDetail();
                readMonth.setTime(time.getEpochSecond());
                readMonth.setPeakDischargeReading(peakOut == null ? 0 : peakOut);
                readMonth.setFlatDischargeReading(flatOut == null ? 0 : flatOut);
                readMonth.setTipDischargeReading(tipOut == null ? 0 : tipOut);
                readMonth.setVallyDischargeReading(valleyOut == null ? 0 : valleyOut);
                readMonth.setDeepVallyDischargeReading(deepValleyOut == null ? 0 : deepValleyOut);
                readMonth.setPeakChargeReading(peakIn == null ? 0 : peakIn);
                readMonth.setFlatChargeReading(flatIn == null ? 0 : flatIn);
                readMonth.setTipChargeReading(tipIn == null ? 0 : tipIn);
                readMonth.setVallyChargeReading(valleyIn == null ? 0 : valleyIn);
                readMonth.setDeepVallyChargeReading(deepValleyIn == null ? 0 : deepValleyIn);
                list.add(readMonth);
            }
        }
        return list;
    }
}
