package com.wifochina.modules.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wifochina.modules.report.entity.DayReportEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * day report Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-19 16:35:26
 */
public interface DayReportMapper extends BaseMapper<DayReportEntity> {

    List<DayReportEntity> findExistingIds(List<DayReportEntity> dayReportEntities);

    List<DayReportEntity> findEveryEquipmentMonthlyAggregatedReports(
            @Param("projectId") String projectId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<DayReportEntity> findDayAggregatedReports(
            @Param("projectId") String projectId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<DayReportEntity> findMonthlyAggregatedReports(
            @Param("projectId") String projectId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    DayReportEntity findSumByCondition(
            @Param("projectIds") List<String> projectIds,
            @Param("equipmentType") String equipmentType,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    List<DayReportEntity> findDayAllReports(
            @Param("projectId") String projectId,
            @Param("equipmentType") String equipmentType,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);
}
