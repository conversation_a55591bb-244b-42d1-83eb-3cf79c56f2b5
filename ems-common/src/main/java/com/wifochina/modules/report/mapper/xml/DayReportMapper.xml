<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wifochina.modules.report.mapper.DayReportMapper">

    <resultMap id="existingIdMap" type="com.wifochina.modules.report.entity.DayReportEntity">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="time" column="time"/>
    </resultMap>

    <select id="findExistingIds" resultMap="existingIdMap">
        SELECT id, project_id, equipment_id, time
        FROM t_day_report
        WHERE (project_id, equipment_id, time) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.projectId}, #{item.equipmentId}, #{item.time})
        </foreach>
    </select>


    <select id="findEveryEquipmentMonthlyAggregatedReports" resultType="com.wifochina.modules.report.entity.DayReportEntity">
        SELECT project_id,
               equipment_id,
               equipment_type,
               SUM(in_data)           AS inData,
               SUM(out_data)          AS outData,
               SUM(out_ems_dcdc_data) AS outEmsDcdcData,
               UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-01 00:00:00')) AS time
        FROM
            t_day_report
        WHERE
            project_id = #{projectId}
          AND time BETWEEN #{startTime}
          AND #{endTime}
        GROUP BY
            UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-01 00:00:00')), equipment_id, equipment_type
        ORDER BY
            time ASC
    </select>


    <select id="findDayAggregatedReports" resultType="com.wifochina.modules.report.entity.DayReportEntity">
        SELECT equipment_type,
            time,
            SUM(in_data)           AS inData,
            SUM(out_data)          AS outData,
            SUM(out_ems_dcdc_data) AS outEmsDcdcData,
            project_id
        FROM
            t_day_report
        WHERE
            project_id = #{projectId}
          and time BETWEEN #{startTime}
          and #{endTime}
        group by equipment_type, time
    </select>

    <select id="findDayAllReports" resultType="com.wifochina.modules.report.entity.DayReportEntity">
        SELECT time,
            SUM(in_data)           AS inData,
            SUM(out_data)          AS outData,
            SUM(out_ems_dcdc_data) AS outEmsDcdcData,
            project_id
        FROM
            t_day_report
        WHERE
            project_id = #{projectId}
          and equipment_type = #{equipmentType}
          and time BETWEEN #{startTime}
          and #{endTime}
        group by time
    </select>

    <select id="findMonthlyAggregatedReports" resultType="com.wifochina.modules.report.entity.DayReportEntity">
        SELECT project_id,
               equipment_type,
               SUM(in_data)           AS inData,
               SUM(out_data)          AS outData,
               SUM(out_ems_dcdc_data) AS outEmsDcdcData,
               UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-01 00:00:00')) AS time
        FROM
            t_day_report
        WHERE
            project_id = #{projectId}
          AND time BETWEEN #{startTime}
          AND #{endTime}
        GROUP BY
            UNIX_TIMESTAMP(DATE_FORMAT(FROM_UNIXTIME(time), '%Y-%m-01 00:00:00')), equipment_type
        ORDER BY
            time ASC
    </select>


    <select id="findSumByCondition" resultType="com.wifochina.modules.report.entity.DayReportEntity">
        SELECT
        SUM(in_data) AS inData,
        SUM(out_data) AS outData,
        sum(out_ems_dcdc_data) AS outEmsDcdcData
        FROM t_day_report
        WHERE project_id IN
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        <if test="equipmentType != null">
            AND equipment_type = #{equipmentType}
        </if>
        <if test="startTime != null and endTime != null">
            AND time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>


</mapper>
