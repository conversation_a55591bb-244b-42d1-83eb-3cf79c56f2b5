package com.wifochina.modules.group.service.impl

import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.service.GroupService
import com.wifochina.modules.group.service.GroupServiceKt
import com.wifochina.modules.oauth.util.WebUtils
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

/**
 * Created on 2024/10/30 09:46.
 * <AUTHOR>
 */
@Service
class GroupServiceKtImpl(
    val groupService: GroupService,
) : GroupServiceKt {

    /**
     * 根据传入的值 更新所有分组的 demandCalcModel 和 demandPeriod 和 splitTime
     */
    override fun syncUpdateDemandParams(demandCalcModel: Int, demandPeriod: Int, splitTime: Int) {
        log.debug { "syncUpdateDemandParams start demandCalcModel: $demandCalcModel , demandPeriod: $demandPeriod , splitTime: $splitTime" }
        groupService.updateBatchById(groupService.list().map {
            it.apply {
                this.demandCalcModel = demandCalcModel
                this.demandPeriod = demandPeriod
                this.slipTime = slipTime
            }
        })
        log.debug { "syncUpdateDemandParams end" }
    }

    override fun controlAgcAndAvc(groupEntity: GroupEntity) {
        val projectId = WebUtils.projectId.get()
        groupEntity.agcController?.takeIf { it }
            ?.let {
                groupService.lambdaUpdate()
                    .eq(GroupEntity::getProjectId, projectId)
                    .ne(GroupEntity::getId, groupEntity.id)
                    .set(GroupEntity::getAgcController, 0)
                    .update()
            }
        groupEntity.avcController?.takeIf { it }
            ?.let {
                groupService.lambdaUpdate()
                    .eq(GroupEntity::getProjectId, projectId)
                    .ne(GroupEntity::getId, groupEntity.id)
                    .set(GroupEntity::getAvcController, 0)
                    .update()
            }
    }
}