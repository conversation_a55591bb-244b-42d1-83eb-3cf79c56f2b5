package com.wifochina.modules.strategytemplate.common

import com.wifochina.modules.group.request.go.GoTimeSlot
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity
import org.springframework.stereotype.Service

/**
 * Created on 2025/5/9 11:12.
 * <AUTHOR>
 */
@Service
class BackFlowLimitTransform : TransformGoTimeSlot<TimeSharingBackFlowLimitEntity> {

    override fun transform(
        data: List<TimeSharingBackFlowLimitEntity>,
        context: TransformGoTimeSlot.TransformContext.() -> Unit
    ): List<GoTimeSlot> {
        val ctx = TransformGoTimeSlot.TransformContext().apply(context)
        return data.map { backFlowLimitEntity ->
            val goTimeSlot = GoTimeSlot()
            goTimeSlot.start_minute = (backFlowLimitEntity.startTime.hour * 60 + backFlowLimitEntity.startTime.minute)
            if (backFlowLimitEntity.endTime.hour == 0 && backFlowLimitEntity.endTime.minute == 0) {
                goTimeSlot.end_minute = 1440
            } else {
                goTimeSlot.end_minute = backFlowLimitEntity.endTime.hour * 60 + backFlowLimitEntity.endTime.minute
            }
            goTimeSlot.power = backFlowLimitEntity.backFlowLimitPower
            goTimeSlot
        }.sortedBy { goTimeSlot -> goTimeSlot.start_minute }

    }
}