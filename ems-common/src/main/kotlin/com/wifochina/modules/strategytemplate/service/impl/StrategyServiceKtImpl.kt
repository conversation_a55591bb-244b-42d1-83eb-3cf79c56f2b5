package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategy.mapper.StrategyMapper
import com.wifochina.modules.strategy.request.ImportGroupRequest
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyConverter
import com.wifochina.modules.strategytemplate.converter.StrategyDayTemplateBindConverter
import com.wifochina.modules.strategytemplate.converter.StrategyMonthTemplateBindConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemConverter
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyDayTemplateBindMapper
import com.wifochina.modules.strategytemplate.service.StrategyControlService
import com.wifochina.modules.strategytemplate.service.StrategyDayTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyMonthTemplateBindService
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Created on 2025/4/22 14:20.
 * <AUTHOR>
 */
@Service
class StrategyServiceKtImpl(
    val strategyTemplateItemControlService: StrategyTemplateItemControlService,
    val strategyControlService: StrategyControlService,
) : ServiceImpl<StrategyMapper, StrategyEntity>(), StrategyServiceKt {


    override fun removeByStrategyDates(projectId: String, groupId: String, strategyDates: List<String>) {
        if (strategyDates.isNotEmpty()) {
            this.remove(
                LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, projectId)
                    .eq(StrategyEntity::getGroupId, groupId).`in`(StrategyEntity::getStrategyDate, strategyDates)
            )
        }
    }

    override fun rebuildDayStrategyWithItem(
        dayBindInfo: StrategyDayTemplateBindEntity,
        items: List<StrategyTemplateItemEntity>,
        strategyTemplateEntity: StrategyTemplateEntity,
    ): List<StrategyEntity> {
        //处理 real StrategyEntity
        val dayStrategyRealEntities = mutableListOf<StrategyEntity>()

        val itemControlIds = items.filter { it.itemControlId != null }.map { it.itemControlId!! }
        val itemControlMaps = strategyTemplateItemControlService.selectControlByIds(itemControlIds)
//        //删除 strategy control 这一天的
        strategyControlService.remove(
            KtQueryWrapper(StrategyControlEntity::class.java).eq(
                StrategyControlEntity::projectId, dayBindInfo.projectId
            ).eq(StrategyControlEntity::groupId, dayBindInfo.groupId)
                .eq(StrategyControlEntity::strategyDate, dayBindInfo.getStrategyDateStr())
        )
        val dayStrategyEntities = items.map { itemEntity ->
            val strategyControlEntity = itemControlMaps[itemEntity.itemControlId]?.let { itemControl ->
                val strategyControl = StrategyControlConverter.INSTANCE.itemControlVoToStrategyControl(itemControl)
                strategyControl.projectId = dayBindInfo.projectId
                strategyControl.groupId = dayBindInfo.groupId
                strategyControl.strategyDate = dayBindInfo.getStrategyDateStr()
                strategyControl.id = null
                strategyControl
            }
            strategyControlEntity?.let {
                strategyControlService.save(it)
            }

            StrategyTemplateItemConverter.INSTANCE.entity2StrategyEntity(itemEntity).apply {
                groupId = dayBindInfo.groupId
                strategyDate = dayBindInfo.getStrategyDateStr()
                // 充放电类型 来自模版 只有海外有
                strategyType = strategyTemplateEntity.strategyType
                //把strategy control id 设置到 item 上
                strategyControlId = strategyControlEntity?.id
            }
        }
        dayStrategyRealEntities.addAll(dayStrategyEntities)
        return dayStrategyRealEntities
    }


    override fun predicateYearlyStrategyMap(group: GroupEntity): Map<String, List<YearlyStrategy>> {
        // 国内还是 国外

        //如果是国内的直接查询
        //先实现 国内的
        val list = this.list(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, WebUtils.projectId.get())
                .eq(StrategyEntity::getGroupId, group.id).isNull(StrategyEntity::getWeekDay)
        )
        val strategyDateMap = list.groupBy { it.strategyDate }
        val yearlyStrategyMap = sortDates(list.map { it.strategyDate }).associateWith {
            val strategyEntities = strategyDateMap[it]!!
            //得到每天的 yearlyStrategyMap
            strategyEntities.map { strategyEntity ->
                val yearlyStrategy = YearlyStrategy()
                yearlyStrategy.start_minute = (strategyEntity.startTime.hour * 60 + strategyEntity.startTime.minute)
                if (strategyEntity.endTime.hour == 0 && strategyEntity.endTime.minute == 0) {
                    yearlyStrategy.end_minute = 1440
                } else {
                    yearlyStrategy.end_minute = strategyEntity.endTime.hour * 60 + strategyEntity.endTime.minute
                }
                yearlyStrategy.function = strategyEntity.type
                yearlyStrategy.power = strategyEntity.power
                if (group.enableStopSoc) {
                    yearlyStrategy.soc = strategyEntity.soc?.toInt()
                }
                yearlyStrategy
            }.sortedBy { yearlyStrategy -> yearlyStrategy.start_minute }
        }
        return yearlyStrategyMap

    }

    override fun getStrategyByGroupIdNew(projectId: String, groupId: String): Map<String, List<StrategyEntity>> {
        val superStrategy = this.getOne(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getWeekDay, 0)
                .eq(StrategyEntity::getGroupId, groupId).eq(StrategyEntity::getProjectId, projectId)
        )
        val subStrategies = this.list(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, projectId)
                .eq(StrategyEntity::getGroupId, groupId).isNull(StrategyEntity::getWeekDay)
        )

        val controlIds = subStrategies.map { it.strategyControlId }
        val controlMaps = strategyControlService.selectControlByIds(controlIds)
        // 分组，比如按 weekDay 字段（或你自己指定的字段）
        val result = subStrategies.groupBy {
            it.strategyDate
        }.toMutableMap()
        result.values.forEach { strategyList ->
            strategyList.forEach { strategy ->
                strategy.itemControl = controlMaps[strategy.strategyControlId]
            }
        }
        result["0"] = listOf(superStrategy)
        return result
    }

    override fun getStrategySystemByGroupIdNew(projectId: String, groupId: String): Map<String, StrategyEntity> {
        val superStrategy = this.getOne(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getWeekDay, 0)
                .eq(StrategyEntity::getGroupId, groupId).eq(StrategyEntity::getProjectId, projectId)
        )
        val result = mapOf(
            "0" to superStrategy
        )
        return result
    }


    fun sortDates(dates: List<String>): List<String> {
        return dates.sortedWith(compareBy({ it.substring(0, 2).toInt() }, // 月份
                                          { it.substring(3, 5).toInt() }  // 日
        ))
    }
}