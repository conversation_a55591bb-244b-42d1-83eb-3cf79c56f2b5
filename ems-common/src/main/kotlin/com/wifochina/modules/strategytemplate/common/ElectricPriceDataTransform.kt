package com.wifochina.modules.strategytemplate.common

import com.wifochina.common.constants.ElectricPriceSpanEnum
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.group.request.go.YearlyStrategy
import org.springframework.stereotype.Service
import java.time.ZoneId

/**
 * Created on 2025/5/9 11:11.
 * <AUTHOR>
 */
@Service
class ElectricPriceDataTransform : TransformYearlyStrategy<ElectricPriceSystemData.Data> {

    override fun transform(
        data: List<ElectricPriceSystemData.Data>,
        context: TransformYearlyStrategy.TransformContext.() -> Unit
    ): List<YearlyStrategy> {
        val ctx = TransformYearlyStrategy.TransformContext().apply(context)
        val zero = MyTimeUtil.getZonedStartOfDayEpoch(ctx.strategyDateStr, ctx.project.timezone)
        return data.map { priceData ->
            var minEndFixedFlag = false
            if (zero == priceData.startTimeUnix.toLong()) {
                minEndFixedFlag = true
            }
            val yearlyStrategy = YearlyStrategy()
            yearlyStrategy.start_minute = MyTimeUtil.getMinutesInDayNew(
                priceData.startTimeUnix.toLong(), ctx.project.timezone,
                minEndFixedFlag
            )
            priceData.startTimeUnix
            val endTimeUnix: Int =
                (priceData.startTimeUnix + ElectricPriceSpanEnum.getValue(ctx.project.electricPriceSpan))
//            if (endTimeUnix.toLong() == zero + EmsConstants.ONE_DAY_SECOND) {
//                minEndFixedFlag = true
//            }
            yearlyStrategy.end_minute = MyTimeUtil.getMinutesInDayNew(
                endTimeUnix.toLong(), ctx.project.timezone, minEndFixedFlag
            )
            yearlyStrategy.function = ctx.strategy.type
            yearlyStrategy.power = ctx.strategy.power
            if (ctx.group?.enableStopSoc == true) {
                yearlyStrategy.soc = ctx.strategy.soc?.toInt()
            }
            yearlyStrategy
        }.sortedBy { yearlyStrategy -> yearlyStrategy.start_minute }

    }
}