package com.wifochina.modules.strategytemplate.request

import io.swagger.annotations.ApiModelProperty

/**
 * Created on 2025/4/21 18:14.
 * <AUTHOR>
 */
data class StrategyItemControlRequest(
    var id: Long?,
    var strategyItemId: Long?,
    //--------差额------
    var priceDifferenceController: Boolean? = false,
    var priceDifference: Double?,

//--------定值------
    var priceBenchmarkController: Boolean? = false,
    var priceBenchmarkCondition: String?,
    var priceBenchmark: Double?,


//--------最值------
    var priceBaseValueController: Boolean?,
    var priceBaseValueRangeCondition: String?,
    var priceBaseValueSectionCount: Int?,


//--------连续时长------
    var priceContinueDurationController: Boolean?,
    var priceContinueDurationCondition: String?,
    var priceContinueDurationHour: Int?
)