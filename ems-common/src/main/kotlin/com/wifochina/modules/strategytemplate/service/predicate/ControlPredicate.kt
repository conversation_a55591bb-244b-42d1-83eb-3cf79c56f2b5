package com.wifochina.modules.strategytemplate.service.predicate

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.wifochina.common.time.MyTimeUtil
import com.wifochina.common.util.EmsConstants
import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.operation.service.ElectricPriceService
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.common.ElectricPriceDataTransform
import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils
import com.wifochina.modules.strategytemplate.common.StrategyTransform
import com.wifochina.modules.strategytemplate.common.TransformYearlyStrategy
import com.wifochina.modules.strategytemplate.entity.transformTimeUnix
import com.wifochina.modules.strategytemplate.enums.ControlPatternLabelEnums
import com.wifochina.modules.strategytemplate.enums.ModelStrategyEnums
import com.wifochina.modules.strategytemplate.enums.StrategyChargeDischargeTypeEnums
import com.wifochina.modules.strategytemplate.service.PredicateContext
import com.wifochina.modules.strategytemplate.service.PredicateModel
import com.wifochina.modules.strategytemplate.service.StrategyControlService
import com.wifochina.modules.strategytemplate.service.StrategyServiceKt
import com.wifochina.modules.strategytemplate.service.dependencypredicate.ControlDependencyPatternPredicate
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/4/29 14:34.
 * <AUTHOR>
 */
@Component
class ControlPredicate(
    val electricPriceService: ElectricPriceService,
    val strategyServiceKt: StrategyServiceKt,
    val strategyControlService: StrategyControlService,
    val controlPatternPredicates: MutableList<out ControlPatternPredicate>,
    val controlDependencyPatternPredicates: MutableList<out ControlDependencyPatternPredicate>,
    val electricPriceDataTransform: ElectricPriceDataTransform,
    val strategyTransform: StrategyTransform,
) : PredicateModel {
    override fun model(): ModelStrategyEnums {
        return ModelStrategyEnums.CONTROL
    }

    override fun predicate(context: PredicateContext): Map<String, List<YearlyStrategy>> {
        // control 模式
        val group = context.group!!
        val project = context.project
        val date = context.date ?: MyTimeUtil.getTodayZeroTime(project.timezone)
        val localDate = Instant.ofEpochSecond(date).atZone(ZoneId.of(project.timezone)).toLocalDate()
        val allDayElectricPrice = electricPriceService.getElectricPriceSystemDataUsePriceProxy(
            date, project
        )
        if (allDayElectricPrice == null || allDayElectricPrice.isEmpty()) {
            return mapOf()
        }
        val strategyDateStr = StrategyCommonUtils.getStrategyDateStr(project, date)
        //只查询 今天的
        // maybe more but just only choose  today
        //TODO 1.要改的地方 如果是模版预览 需要改成 strategyItem的查询 并且是查询这个模版的所有
        val list = strategyServiceKt.list(
            LambdaQueryWrapper<StrategyEntity>().eq(StrategyEntity::getProjectId, project.id)
                .eq(StrategyEntity::getGroupId, group.id).isNull(StrategyEntity::getWeekDay)
                .eq(StrategyEntity::getStrategyDate, strategyDateStr)
        )

        val selfProductConsumerStrategyList = list.filter { it.type.equals(2) }
        //充放电的 需要找到对应的control
        val chargeDischargeStrategyList = list.filter { !it.type.equals(2) }
        // 得到 item control的 map
        //TODO 2.如果是模版预览, 需要改成从 strategy item controller 里查询 controllMap
        val strategyControlMap =
            strategyControlService.selectControlByIds(chargeDischargeStrategyList.filter { it.strategyControlId != null }
                                                          .map { it.strategyControlId })
        val chargeStrategyControlResults = mutableListOf<YearlyStrategy>()
        val dischargeStrategyControlResults = mutableListOf<YearlyStrategy>()

        //自发自用  应该是和simple模式一样的处理方式
        var selfProductConsumerResults: List<YearlyStrategy> = listOf()
        selfProductConsumerStrategyList.forEach { strategy ->
            var endZeroFlag = false
            if (strategy.equals(LocalTime.MIDNIGHT)) {
                endZeroFlag = true
            }
            val strategyContext =
                StrategyPredicateContext(context, strategy, allDayElectricPrice).calStrategyPeriodElectricPrices(
                    strategy.startTime.transformTimeUnix(localDate, ZoneId.of(project.timezone)),
                    if (!endZeroFlag) strategy.endTime.transformTimeUnix(
                        localDate, ZoneId.of(project.timezone)
                    ) else strategy.endTime.transformTimeUnix(
                        localDate,
                        ZoneId.of(project.timezone)
                    ) + EmsConstants.ONE_DAY_SECOND
                )

            selfProductConsumerResults = electricPriceDataTransform.transform(
                strategyContext.strategyPeriodElectricPrices
            ) {
                this.project = project
                this.group = group
                this.strategy = strategy
                this.strategyDateStr = strategyDateStr
            }
        }
        try {
            chargeDischargeStrategyList.forEach strategy@{ strategy ->
                strategy.endTime
                var endZeroFlag = false
                if (strategy.endTime == LocalTime.MIDNIGHT) {
                    endZeroFlag = true
                }
                val strategyContext =
                    StrategyPredicateContext(context, strategy, allDayElectricPrice).calStrategyPeriodElectricPrices(
                        strategy.startTime.transformTimeUnix(localDate, ZoneId.of(project.timezone)),
                        if (!endZeroFlag) strategy.endTime.transformTimeUnix(
                            localDate,
                            ZoneId.of(project.timezone)
                        ) else strategy.endTime.transformTimeUnix(
                            localDate,
                            ZoneId.of(project.timezone)
                        ) + EmsConstants.ONE_DAY_SECOND
                    )
                if (strategyContext.strategyPeriodElectricPrices.isEmpty()) {
                    return@strategy
                }
                val controlResultMap = mutableMapOf<String, List<ElectricPriceSystemData.Data>>()
                val transformContext: TransformYearlyStrategy.TransformContext.() -> Unit = {
                    this.project = project
                    this.group = group
                    this.strategy = strategy
                    this.strategyDateStr = strategyDateStr
                }
                strategyControlMap[strategy.strategyControlId]?.let { control ->
                    if (control.priceBaseValueController == false && control.priceBenchmarkController == false && control.priceDifferenceController == false) {
                        when (StrategyChargeDischargeTypeEnums.valueOfByType(strategy.type)) {
                            StrategyChargeDischargeTypeEnums.CHARGE -> {
                                chargeStrategyControlResults.add(
                                    strategyTransform.transform(
                                        listOf(strategy)
                                    ) {
                                        this.project = project
                                        this.group = group
                                        this.strategy = strategy
                                    }[0]
                                )
                            }

                            StrategyChargeDischargeTypeEnums.DISCHARGE -> {
                                dischargeStrategyControlResults.add(
                                    strategyTransform.transform(
                                        listOf(strategy)
                                    ) {
                                        this.project = project
                                        this.group = group
                                        this.strategy = strategy
                                    }[0]
                                )
                            }
                        }
                        return@strategy
                    }
                    val controlPatternPredicateContext = ControlPatternPredicate.ControlPatternPredicateContext(
                        strategyContext, control
                    )
                    controlPatternPredicates.forEach { predicate ->
                        val predicateResult = predicate.patternPredicate(
                            controlPatternPredicateContext
                        )
                        if (predicateResult.skip) {
                            //直接跳过当前的策略 到下一个策略
                            return@strategy
                        }
                        //结果
                        when (StrategyChargeDischargeTypeEnums.valueOfByType(strategy.type)) {
                            StrategyChargeDischargeTypeEnums.CHARGE -> controlResultMap[predicate.patternLabel().name] =
                                predicateResult.matchResult

                            StrategyChargeDischargeTypeEnums.DISCHARGE -> controlResultMap[predicate.patternLabel().name] =
                                predicateResult.matchResult
                        }
                    }
                    //3个predicate 结束后 要进行continue的predicate执行..
                    val controlResultToIntersect = listOfNotNull(
                        if (control.priceBaseValueController!!) controlResultMap[ControlPatternLabelEnums.BASE_VALUE.name] else null,
                        if (control.priceDifferenceController!!) controlResultMap[ControlPatternLabelEnums.DIFFERENCE_VALUE.name] else null,
                        if (control.priceBenchmarkController!!) controlResultMap[ControlPatternLabelEnums.BENCHMARK_VALUE.name] else null,
                    )
                    if (controlResultToIntersect.isEmpty()) {
                        return@strategy
                    }
                    var intersect =
                        controlResultToIntersect.reduce { acc, list -> acc.intersect(list.toSet()).toList() }
                    controlDependencyPatternPredicates.forEach { dependencyPredicate ->
                        //for extend in future ? ... do you understand
                        intersect = dependencyPredicate.dependencyPatternPredicate(
                            ControlDependencyPatternPredicate.DependencyPredicateContext(
                                controlPatternPredicateContext, intersect
                            )
                        ).matchResult

                    }
                    val yearlyStrategies = electricPriceDataTransform.transform(intersect, transformContext)
                    when (StrategyChargeDischargeTypeEnums.valueOfByType(strategy.type)) {
                        StrategyChargeDischargeTypeEnums.CHARGE -> chargeStrategyControlResults.addAll(yearlyStrategies)
                        StrategyChargeDischargeTypeEnums.DISCHARGE -> dischargeStrategyControlResults.addAll(
                            yearlyStrategies
                        )
                    }
                } ?: run {
                    //无控制..
                    // 不知道这里有没有 如果可以不设置条件 那么这里应该要有
                    val yearlyStrategies = electricPriceDataTransform.transform(
                        strategyContext.strategyPeriodElectricPrices, transformContext
                    )
                    //带冲放的策略 需要最后控制一下 交集问题 ->转成不冲不放
                    when (StrategyChargeDischargeTypeEnums.valueOfByType(strategy.type)) {
                        StrategyChargeDischargeTypeEnums.CHARGE -> chargeStrategyControlResults.addAll(yearlyStrategies)
                        StrategyChargeDischargeTypeEnums.DISCHARGE -> dischargeStrategyControlResults.addAll(
                            yearlyStrategies
                        )
                    }
                }
            }
        } catch (e: Exception) {
            log.error { "chargeDischargeStrategyList forEach error : $e" }
        }
        //所有策略结束后 要把冲放2个结果 进行交集 交集要踢出了 表示补充不放
        val result = mutableListOf<List<YearlyStrategy>>()
        val newChargeList = mutableListOf<YearlyStrategy>()
        val newDischargeList = mutableListOf<YearlyStrategy>()

        if (chargeStrategyControlResults.isEmpty() && dischargeStrategyControlResults.isNotEmpty()) {
            newDischargeList.addAll(dischargeStrategyControlResults)
        } else if (dischargeStrategyControlResults.isEmpty() && chargeStrategyControlResults.isNotEmpty()) {
            newChargeList.addAll(chargeStrategyControlResults)
        } else {
            log.info { "chargeStrategyControlResults: $chargeStrategyControlResults" }
            for (charge in chargeStrategyControlResults) {
                val overlappingDischarges = dischargeStrategyControlResults.filter {
                    it.end_minute > charge.start_minute && it.start_minute < charge.end_minute
                }

                val cutCharges = subtractOverlapsFromStrategy(charge, overlappingDischarges)
                newChargeList.addAll(cutCharges)
            }
            log.info { "dischargeStrategyControlResults: $dischargeStrategyControlResults" }
            for (discharge in dischargeStrategyControlResults) {
                val overlappingCharges = chargeStrategyControlResults.filter {
                    it.end_minute > discharge.start_minute && it.start_minute < discharge.end_minute
                }

                val cutDischarges = subtractOverlapsFromStrategy(discharge, overlappingCharges)
                newDischargeList.addAll(cutDischarges)
            }
        }
        log.info { "charge over laps result: $newChargeList" }
        log.info { "discharge over laps result: $newDischargeList" }
        result.add(newChargeList)
        result.add(newDischargeList)
        log.info { "selfProductConsumerResults result:$selfProductConsumerResults" }
        result.add(selfProductConsumerResults)
        val flatten = result.flatten()
        return mapOf(
            strategyDateStr to flatten
        )
    }

    fun subtractOverlapsFromStrategy(
        base: YearlyStrategy, overlaps: List<YearlyStrategy>
    ): List<YearlyStrategy> {
        var currentStart = base.start_minute
        val result = mutableListOf<YearlyStrategy>()

        val sortedOverlaps = overlaps.sortedBy { it.start_minute }

        for (overlap in sortedOverlaps) {
            if (overlap.end_minute <= currentStart) continue
            if (overlap.start_minute >= base.end_minute) break

            if (overlap.start_minute > currentStart) {
                val part = base.clone()
                part.start_minute = currentStart
                part.end_minute = minOf(overlap.start_minute, base.end_minute)
                result.add(part)
            }
            currentStart = maxOf(currentStart, overlap.end_minute)
        }

        if (currentStart < base.end_minute) {
            val part = base.clone()
            part.start_minute = currentStart
            part.end_minute = base.end_minute
            result.add(part)
        }
        return result
    }


}