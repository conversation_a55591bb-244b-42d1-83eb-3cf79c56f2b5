package com.wifochina.modules.strategytemplate.service

import com.baomidou.mybatisplus.extension.service.IService
import com.wifochina.modules.group.entity.GroupEntity
import com.wifochina.modules.group.request.go.YearlyStrategy
import com.wifochina.modules.strategy.entity.StrategyEntity
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateItemEntity

/**
 * Created on 2025/4/22 14:20.
 * <AUTHOR>
 */
interface StrategyServiceKt : IService<StrategyEntity> {


    fun removeByStrategyDates(projectId: String, groupId: String, strategyDates: List<String>)


    fun rebuildDayStrategyWithItem(
        dayBindInfo: StrategyDayTemplateBindEntity,
        items: List<StrategyTemplateItemEntity>,
        strategyTemplateEntity: StrategyTemplateEntity,
    ): List<StrategyEntity>

    fun predicateYearlyStrategyMap(group: GroupEntity): Map<String, List<YearlyStrategy>>

    fun getStrategyByGroupIdNew(projectId: String, groupId: String): Map<String, List<StrategyEntity>>
    fun getStrategySystemByGroupIdNew(projectId: String, groupId: String): Map<String, StrategyEntity>

}
