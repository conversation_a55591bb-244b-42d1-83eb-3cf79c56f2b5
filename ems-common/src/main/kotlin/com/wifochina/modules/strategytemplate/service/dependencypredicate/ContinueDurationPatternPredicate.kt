package com.wifochina.modules.strategytemplate.service.dependencypredicate

import com.wifochina.modules.electric.vo.ElectricPriceSystemData
import com.wifochina.modules.strategytemplate.enums.ConditionEnums
import com.wifochina.modules.strategytemplate.service.predicate.PredicateResult
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service


private val log = KotlinLogging.logger { }

/**
 * Created on 2025/5/8 16:51.
 * <AUTHOR>
 */
@Service

class ContinueDurationPatternPredicate : ControlDependencyPatternPredicate {
    override fun dependencyPatternPredicate(context: ControlDependencyPatternPredicate.DependencyPredicateContext): PredicateResult {
        return takeIf { context.controlPatternPredicateContext.control.priceContinueDurationController == true }?.let {
            PredicateResult().apply {
                skip = false
                matchResult = checkContinueDurationSegment(
                    context.dependencyResultPrices,
                    ConditionEnums.valueOf(context.controlPatternPredicateContext.control.priceContinueDurationCondition!!),
                    context.controlPatternPredicateContext.control.priceContinueDurationHour!!
                )
                log.info { "price continue duration match prices : $matchResult" }
            }
        } ?: PredicateResult().apply {
            skip = false
            matchResult = context.dependencyResultPrices
        }
    }

    fun checkContinueDurationSegment(
        dataList: List<ElectricPriceSystemData.Data>, condition: ConditionEnums, requiredDurationInHours: Int
    ): List<ElectricPriceSystemData.Data> {
        if (dataList.isEmpty()) return emptyList()

        val sorted = dataList.sortedBy { it.startTimeUnix }
        val result = mutableListOf<ElectricPriceSystemData.Data>()
        val requiredSeconds = requiredDurationInHours * 3600

        fun durationMatches(seconds: Int): Boolean {
            return when (condition) {
                ConditionEnums.EQUAL -> seconds == requiredSeconds
                ConditionEnums.NOT_EQUAL -> seconds != requiredSeconds
                ConditionEnums.GT -> seconds > requiredSeconds
                ConditionEnums.GTE -> seconds >= requiredSeconds
                ConditionEnums.LT -> seconds < requiredSeconds
                ConditionEnums.LTE -> seconds <= requiredSeconds
            }
        }

        val validSegments = mutableListOf<List<ElectricPriceSystemData.Data>>()

        for (start in sorted.indices) {
            val segment = mutableListOf(sorted[start])
            var accumulatedSeconds = sorted[start].intervalSeconds
            var j = start + 1

            // 特殊处理EQUAL条件：先检查单点是否满足
            if (condition == ConditionEnums.EQUAL && accumulatedSeconds == requiredSeconds) {
                validSegments.add(segment.toList())
                // 继续尝试扩展，看是否能形成更长的段（虽然不会被使用，但保持逻辑一致）
            }
            if (condition == ConditionEnums.LTE && accumulatedSeconds == requiredSeconds) {
                validSegments.add(segment.toList())
            }
            if (condition == ConditionEnums.GTE && accumulatedSeconds == requiredSeconds) {
                validSegments.add(segment.toList())
            }
            while (j < sorted.size) {
                val last = segment.last()
                val current = sorted[j]
                val expectedStart = last.startTimeUnix + last.intervalSeconds

                if (current.startTimeUnix == expectedStart) {
                    // 对于EQUAL条件，如果添加下一个点会超过目标时长，则停止
                    if (condition == ConditionEnums.EQUAL && accumulatedSeconds + current.intervalSeconds > requiredSeconds) {
                        break
                    }
                    segment.add(current)
                    accumulatedSeconds += current.intervalSeconds

                    // 检查当前段是否满足条件
                    if (durationMatches(accumulatedSeconds)) {
                        validSegments.add(segment.toList())

                        // 对于EQUAL条件，找到后不需要继续扩展
                        if (condition == ConditionEnums.EQUAL) {
                            break
                        }
                    }

                    j++
                } else {
                    break
                }
            }
        }

        // 合并所有有效段并去重
        val uniqueResults = validSegments.flatten().distinct()
        result.addAll(uniqueResults)

        return result
    }


    /**
     * 判断连续时长 段的方法
     */
    @Deprecated(message = "")
    fun checkContinueDurationSegmentOld(
        dataList: List<ElectricPriceSystemData.Data>, condition: ConditionEnums, requiredDurationInHours: Int
    ): List<ElectricPriceSystemData.Data> {
        if (dataList.isEmpty()) return emptyList()

        val sorted = dataList.sortedBy { it.startTimeUnix }
        val result = mutableListOf<ElectricPriceSystemData.Data>()

        val requiredSeconds = requiredDurationInHours * 3600

        fun durationMatches(seconds: Int): Boolean {
            return when (condition) {
                ConditionEnums.EQUAL -> seconds == requiredSeconds
                ConditionEnums.NOT_EQUAL -> seconds != requiredSeconds
                ConditionEnums.GT -> seconds > requiredSeconds
                ConditionEnums.GTE -> seconds >= requiredSeconds
                ConditionEnums.LT -> seconds < requiredSeconds
                ConditionEnums.LTE -> seconds <= requiredSeconds
            }
        }

        var i = 0
        while (i < sorted.size) {
            val segment = mutableListOf<ElectricPriceSystemData.Data>()
            segment.add(sorted[i])
            var accumulatedSeconds = sorted[i].intervalSeconds
            var j = i + 1

            if (durationMatches(accumulatedSeconds)) {
                result.addAll(segment)
                i++
                continue
            }
            while (j < sorted.size) {
                val last = segment.last()
                val current = sorted[j]
                val expectedStart = last.startTimeUnix + last.intervalSeconds

                if (current.startTimeUnix == expectedStart) {
                    segment.add(current)
                    accumulatedSeconds += current.intervalSeconds
                    if (durationMatches(accumulatedSeconds)) {
                        //重新定位i 的开始点
                        i = j
                        break
                    }
                    j++
                } else {
                    break
                }
            }
            if (durationMatches(accumulatedSeconds)) {
                result.addAll(segment)
            }
            // 无论是否满足，都从下一个数据开始
            i++
        }

        return result
    }
}